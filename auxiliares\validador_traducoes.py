#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Validador de Traduções - Sistema de validação para traduções e naturalizações
"""

import os
from datetime import datetime


class ValidadorTraducoes:
    def __init__(self, api_key_gemini=None):
        """
        Inicializa o validador (apenas validação de tamanho)

        Args:
            api_key_gemini: Não utilizado (mantido para compatibilidade)
        """
        print("✅ Validador configurado (apenas validação de tamanho 80%)")
    
    def extrair_idioma_da_pasta(self, nome_pasta):
        """
        Extrai o código do idioma dos últimos 2 caracteres da pasta
        
        Args:
            nome_pasta: Nome da pasta (ex: "1_video_pt")
            
        Returns:
            str: <PERSON><PERSON><PERSON> do idioma (ex: "pt")
        """
        return nome_pasta[-2:].lower()
    
    def validar_tamanho_arquivo(self, texto_original, texto_traduzido, min_percent=80):
        """
        Valida se o arquivo traduzido tem pelo menos 80% do tamanho original
        
        Args:
            texto_original: Texto original
            texto_traduzido: Texto traduzido
            min_percent: Percentual mínimo (padrão: 80%)
            
        Returns:
            tuple: (bool, float) - (é_válido, percentual_atual)
        """
        if not texto_original or not texto_traduzido:
            return False, 0.0
        
        # Remover espaços em branco excessivos para comparação mais justa
        texto_original_limpo = texto_original.strip()
        texto_traduzido_limpo = texto_traduzido.strip()
        
        if not texto_original_limpo or not texto_traduzido_limpo:
            return False, 0.0
        
        tamanho_original = len(texto_original_limpo)
        tamanho_traduzido = len(texto_traduzido_limpo)
        
        percentual = (tamanho_traduzido / tamanho_original) * 100
        
        return percentual >= min_percent, percentual
    
    # Função removida - validação com Gemini desabilitada
    # Agora usando apenas validação de tamanho (80%)
    
    def validar_traducao(self, texto_original, arquivo_traducao, nome_pasta):
        """
        Valida arquivo de tradução (1_traducao_XX)
        
        Args:
            texto_original: Texto original
            arquivo_traducao: Caminho do arquivo de tradução
            nome_pasta: Nome da pasta para extrair idioma
            
        Returns:
            tuple: (bool, str) - (é_válido, mensagem)
        """
        print(f"🔍 Validando tradução: {arquivo_traducao}")
        
        # Verificar se arquivo existe
        if not os.path.exists(arquivo_traducao):
            return False, f"Arquivo não encontrado: {arquivo_traducao}"
        
        # Ler conteúdo
        try:
            with open(arquivo_traducao, 'r', encoding='utf-8') as f:
                texto_traduzido = f.read()
        except Exception as e:
            return False, f"Erro ao ler arquivo: {e}"
        
        # Validar tamanho
        is_valid_size, percentual = self.validar_tamanho_arquivo(texto_original, texto_traduzido)
        
        if not is_valid_size:
            return False, f"Arquivo muito pequeno: {percentual:.1f}% do original (mínimo: 80%)"
        
        print(f"✅ Tradução válida: {percentual:.1f}% do tamanho original")
        return True, f"Tradução válida ({percentual:.1f}%)"
    
    def validar_naturalizacao(self, texto_original, arquivo_naturalizacao, nome_pasta):
        """
        Valida arquivo de naturalização (2_naturalizacao_XX)
        
        Args:
            texto_original: Texto original
            arquivo_naturalizacao: Caminho do arquivo de naturalização
            nome_pasta: Nome da pasta para extrair idioma
            
        Returns:
            tuple: (bool, str) - (é_válido, mensagem)
        """
        print(f"🔍 Validando naturalização: {arquivo_naturalizacao}")
        
        # Verificar se arquivo existe
        if not os.path.exists(arquivo_naturalizacao):
            return False, f"Arquivo não encontrado: {arquivo_naturalizacao}"
        
        # Ler conteúdo
        try:
            with open(arquivo_naturalizacao, 'r', encoding='utf-8') as f:
                texto_naturalizado = f.read()
        except Exception as e:
            return False, f"Erro ao ler arquivo: {e}"
        
        # Validar tamanho
        is_valid_size, percentual = self.validar_tamanho_arquivo(texto_original, texto_naturalizado)
        
        if not is_valid_size:
            return False, f"Arquivo muito pequeno: {percentual:.1f}% do original (mínimo: 80%)"
        
        print(f"✅ Naturalização válida: {percentual:.1f}% do tamanho original")
        return True, f"Naturalização válida ({percentual:.1f}%)"
    
    # Função removida - validação de roteiro final com Gemini desabilitada
    # Agora o roteiro final é criado diretamente sem validação LLM


def log_validacao(pasta_video, etapa, resultado, mensagem):
    """
    Registra resultado da validação em log
    
    Args:
        pasta_video: Nome da pasta do vídeo
        etapa: Etapa da validação (traducao, naturalizacao, roteiro_final)
        resultado: True/False
        mensagem: Mensagem detalhada
    """
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    status = "✅ VÁLIDO" if resultado else "❌ INVÁLIDO"
    
    log_entry = f"[{timestamp}] {pasta_video} - {etapa}: {status} - {mensagem}"
    print(log_entry)
    
    # Salvar em arquivo de log (opcional)
    try:
        log_file = "validacao_traducoes.log"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except:
        pass  # Ignorar erros de log
