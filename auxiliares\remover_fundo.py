# import os
# from PIL import Image, ImageDraw
# from io import BytesIO
# from rembg import remove # Certifique-se de ter 'rembg' instalado: pip install rembg[cpu]

import os
from io import BytesIO
from typing import Optional, Union, Tuple

from PIL import Image, ImageColor
from rembg import remove


def remover_fundo_e_salvar_com_corte(
    caminho_imagem_entrada: str,
    caminho_imagem_saida: str,
    cor_fundo: Optional[Union[str, Tuple[int, int, int], Tuple[int, int, int, int]]] = None,
    cortar_imagem: bool = False,
    corte_agressivo: bool = False,
    percentuais_agressividade: Tuple[float, float, float, float] = (0.0, 0.0, 0.0, 0.0) # (cima, direita, baixo, esquerda)
):
    """
    Remove o fundo de uma imagem, opcionalmente aplica cortes e salva o resultado
    com um fundo transparente ou com uma cor específica em vários formatos.

    Argumentos:
        caminho_imagem_entrada (str): O caminho completo para o arquivo da imagem de entrada.
        caminho_imagem_saida (str): O caminho completo onde a imagem será salva.
                                    O formato (PNG, JPG, WEBP, etc.) é inferido pela extensão.
        cor_fundo (Optional[Union[str, tuple]]): Cor de fundo para aplicar.
                                                 Pode ser uma string (ex: 'white', '#FF0000') ou
                                                 uma tupla RGB/RGBA (ex: (255, 255, 255)).
                                                 Se None, o fundo será transparente (para formatos que suportam, como PNG).
                                                 Padrão: None.
        cortar_imagem (bool): Se True, a imagem será cortada para remover áreas transparentes
                                excessivas. Padrão: False.
        corte_agressivo (bool): Se True e 'cortar_imagem' for True, um corte adicional
                                será aplicado para dentro da imagem. Padrão: False.
        percentuais_agressividade (tuple[float, float, float, float]): Tupla de 4 floats
                                     representando o percentual de corte agressivo para
                                     (cima, direita, baixo, esquerda). Valores entre 0.0 e 1.0.
                                     Só é considerado se 'corte_agressivo' for True.
    """
    try:
        # 1. Validação do caminho de saída
        diretorio_saida = os.path.dirname(caminho_imagem_saida)
        if diretorio_saida:
            os.makedirs(diretorio_saida, exist_ok=True)

        # 2. Abre a imagem de entrada e converte para RGBA
        with open(caminho_imagem_entrada, 'rb') as f:
            input_bytes = f.read()

        # 3. Remove o fundo usando rembg
        output_bytes = remove(input_bytes)

        # 4. Converte o resultado de volta para PIL (RGBA) para manipulação
        result_image = Image.open(BytesIO(output_bytes)).convert("RGBA")

        largura_final, altura_final = result_image.size
        x1, y1, x2, y2 = 0, 0, largura_final, altura_final
        has_visible_pixel = False

        # 5. Lógica de Corte Automático (se solicitado)
        if cortar_imagem:
            bbox = result_image.getbbox() # Método mais eficiente para encontrar a caixa delimitadora
            if bbox:
                has_visible_pixel = True
                x1, y1, x2, y2 = bbox
            else:
                print(f"Aviso: A imagem em '{caminho_imagem_entrada}' resultou em uma imagem completamente transparente. Salvando como está.")
                result_image.save(caminho_imagem_saida)
                print(f"✓ Imagem vazia salva em: {caminho_imagem_saida}")
                return

        # 6. Lógica de Corte Agressivo (se solicitado)
        if cortar_imagem and corte_agressivo and has_visible_pixel:
            percentuais_norm = [max(0.0, min(1.0, p / 100.0 if p > 1.0 else p)) for p in percentuais_agressividade]
            p_cima, p_direita, p_baixo, p_esquerda = percentuais_norm

            largura_area_visivel = x2 - x1
            altura_area_visivel = y2 - y1

            corte_pixel_esquerda = int(largura_area_visivel * p_esquerda)
            corte_pixel_direita = int(largura_area_visivel * p_direita)
            corte_pixel_cima = int(altura_area_visivel * p_cima)
            corte_pixel_baixo = int(altura_area_visivel * p_baixo)

            nova_x1 = x1 + corte_pixel_esquerda
            nova_y1 = y1 + corte_pixel_cima
            nova_x2 = x2 - corte_pixel_direita
            nova_y2 = y2 - corte_pixel_baixo

            if nova_x1 >= nova_x2: nova_x1 = nova_x2 - 1
            if nova_y1 >= nova_y2: nova_y1 = nova_y2 - 1
            
            x1, y1, x2, y2 = nova_x1, nova_y1, nova_x2, nova_y2

        # 7. Aplica o Corte Final na imagem (se algum corte foi calculado)
        if cortar_imagem and has_visible_pixel:
            result_image = result_image.crop((x1, y1, x2, y2))

        # 8. Prepara a imagem final para salvar (com ou sem fundo)
        imagem_para_salvar = result_image
        output_ext = os.path.splitext(caminho_imagem_saida)[1].lower()

        cor_fundo_final = cor_fundo
        # Se o formato de saída não suporta transparência (ex: JPEG) e nenhuma cor foi dada,
        # usa branco como padrão para evitar um fundo preto.
        if cor_fundo_final is None and output_ext in ['.jpg', '.jpeg']:
            cor_fundo_final = 'white'
            print(f"Info: Formato de saída '{output_ext}' não suporta transparência. Usando fundo branco como padrão.")

        # Se uma cor de fundo foi definida (pelo usuário ou como padrão para JPG), aplica-a.
        if cor_fundo_final:
            # Cria uma nova imagem com a cor de fundo.
            imagem_com_fundo = Image.new("RGBA", result_image.size, cor_fundo_final)
            
            # Cola a imagem com recorte por cima do fundo, usando o canal alfa como máscara.
            imagem_com_fundo.paste(result_image, (0, 0), result_image)
            
            # Converte para 'RGB' se o formato de saída não precisar de alfa.
            # Isto é crucial para salvar corretamente em formatos como JPEG.
            imagem_para_salvar = imagem_com_fundo.convert("RGB")
        
        # 9. Salva a imagem processada final
        # A biblioteca Pillow infere o formato correto a partir da extensão do arquivo.
        save_params = {}
        if output_ext in ['.jpg', '.jpeg']:
            save_params['quality'] = 95 # Boa qualidade para JPEG
            save_params['subsampling'] = 0

        imagem_para_salvar.save(caminho_imagem_saida, **save_params)
        
        # --- Mensagem de status ---
        status_corte = "(sem corte)"
        if cortar_imagem:
            status_corte = "(cortada automaticamente)"
            if corte_agressivo and has_visible_pixel:
                p_c, p_d, p_b, p_e = [int(p * 100) for p in percentuais_norm]
                status_corte = f"(cortada agressivamente: C{p_c}% D{p_d}% B{p_b}% E{p_e}%)"
        
        status_fundo = "com fundo transparente"
        if cor_fundo_final:
            status_fundo = f"com fundo '{str(cor_fundo_final)}'"
        
        print(f"✓ Imagem salva: {caminho_imagem_saida} {status_corte} {status_fundo}")

    except FileNotFoundError:
        print(f"Erro: Arquivo de entrada '{caminho_imagem_entrada}' não encontrado.")
    except Exception as e:
        print(f"Erro inesperado ao processar a imagem: {e}")


def colocar_em_quadro(
    imagem_entrada: str,
    imagem_saida: str,
    tamanho_quadro: tuple[int, int] = (1408, 768),
    cor_fundo = "black"  # pode ser "white", "#ff0000", etc.
):
    """
    Cria um quadro com a imagem colada centralizada.

    Parâmetros:
    - imagem_entrada: Caminho da imagem original.
    - imagem_saida: Caminho para salvar a imagem com quadro.
    - tamanho_quadro: (largura, altura) do quadro.
    - cor_fundo: Cor de fundo (HTML, nome ou RGB).
    """
    # Converte a cor para RGB se for string
    if isinstance(cor_fundo, str):
        cor_rgb = ImageColor.getrgb(cor_fundo)
    else:
        cor_rgb = cor_fundo

    # Carrega imagem de entrada
    img = Image.open(imagem_entrada).convert("RGBA")

    # Cria o quadro de fundo
    quadro = Image.new("RGBA", tamanho_quadro, cor_rgb + (255,))

    # Centraliza
    x = (tamanho_quadro[0] - img.width) // 2
    y = (tamanho_quadro[1] - img.height) // 2
    quadro.paste(img, (x, y), mask=img)

    # Converte para RGB se necessário
    if imagem_saida.lower().endswith(".jpg"):
        quadro = quadro.convert("RGB")

    quadro.save(imagem_saida)
    print(f"Imagem salva com sucesso em: {imagem_saida}")

# def remover_fundo_e_salvar_com_corte(
#     caminho_imagem_entrada: str,
#     caminho_imagem_saida: str,
#     cortar_imagem: bool = False,
#     corte_agressivo: bool = False,
#     percentuais_agressividade: tuple[float, float, float, float] = (0.0, 0.0, 0.0, 0.0) # (cima, direita, baixo, esquerda)
# ):
#     """
#     Remove o fundo de uma imagem, opcionalmente corta as áreas transparentes,
#     e aplica um corte agressivo adicional INDIVIDUAL em cada lado (a partir do corte automático),
#     salvando o resultado em um novo arquivo PNG.

#     Argumentos:
#         caminho_imagem_entrada (str): O caminho completo para o arquivo da imagem de entrada.
#         caminho_imagem_saida (str): O caminho completo onde a imagem sem fundo será salva.
#                                     Deve ter a extensão '.png' para manter a transparência.
#         cortar_imagem (bool): Se True, a imagem resultante será cortada para remover
#                              áreas transparentes excessivas (padrão: False).
#         corte_agressivo (bool): Se True e 'cortar_imagem' também for True, um corte adicional
#                                 será aplicado para dentro da imagem (padrão: False).
#         percentuais_agressividade (tuple[float, float, float, float]): Uma tupla ou lista de 4 floats/ints
#                                                                       representando o percentual de corte agressivo
#                                                                       para cada lado, na ordem: (cima, direita, baixo, esquerda).
#                                                                       Valores entre 0.0 e 1.0 (ou 0 e 100).
#                                                                       Só é considerado se 'corte_agressivo' for True.
#     """
#     try:
#         # 1. Abre a imagem de entrada e converte para RGBA
#         image_pil = Image.open(caminho_imagem_entrada).convert("RGBA")

#         # 2. Converte para bytes para o rembg
#         img_byte_arr = BytesIO()
#         image_pil.save(img_byte_arr, format='PNG')
#         img_byte_arr.seek(0)

#         # 3. Remove o fundo
#         output = remove(img_byte_arr.getvalue())

#         # 4. Converte o resultado de volta para PIL (RGBA)
#         result_image = Image.open(BytesIO(output)).convert("RGBA")

#         largura_final, altura_final = result_image.size
        
#         # Inicializa as coordenadas de corte com a imagem completa
#         # Elas serão ajustadas primeiro pelo corte automático, depois pelo agressivo (se aplicável).
#         x1, y1, x2, y2 = 0, 0, largura_final - 1, altura_final - 1 # x1,y1 = canto superior esquerdo; x2,y2 = canto inferior direito
        
#         has_visible_pixel = False # Flag para verificar se há algum pixel visível para cortar

#         # 5. Lógica de Corte Automático (Identifica as bordas do conteúdo real)
#         # Este é o "corte original" que você mencionou.
#         if cortar_imagem:
#             min_x, min_y, max_x, max_y = largura_final, altura_final, -1, -1
            

#             for x in range(largura_final):
#                 for y in range(altura_final):
#                     alfa = result_image.getpixel((x, y))[3]
#                     if alfa > 0: # Se o pixel não for totalmente transparente
#                         has_visible_pixel = True
#                         min_x = min(min_x, x)
#                         min_y = min(min_y, y)
#                         max_x = max(max_x, x)
#                         max_y = max(max_y, y)

#             if has_visible_pixel:
#                 # Atualiza as coordenadas de corte com as bordas do conteúdo visível
#                 x1, y1, x2, y2 = min_x, min_y, max_x, max_y
#             else:
#                 print("A imagem resultante é completamente transparente, nada para cortar.")
#                 # Se não há pixels visíveis, salva a imagem original transparente (ou vazia) e sai
#                 result_image.save(caminho_imagem_saida)
#                 print(f"✓ Fundo removido e imagem salva (vazia) em: {caminho_imagem_saida}")
#                 return

#         # 6. Lógica de Corte Agressivo (Aplicado COMO um "plus" sobre o corte automático, se ativado)
#         if cortar_imagem and corte_agressivo and has_visible_pixel:
#             # Normaliza os percentuais para a faixa de 0.0 a 1.0
#             percentuais_norm = [max(0.0, min(100.0, float(p))) for p in percentuais_agressividade]
#             percentuais_norm = [p / 100.0 if p > 1.0 else p for p in percentuais_norm]
            
#             p_cima, p_direita, p_baixo, p_esquerda = percentuais_norm

#             # Calcula as dimensões da área visível APÓS o corte automático (base para o agressivo)
#             largura_area_visivel = x2 - x1 + 1
#             altura_area_visivel = y2 - y1 + 1

#             # Calcula o quanto "morder" de cada lado em pixels, baseado na área visível
#             corte_pixel_esquerda = int(largura_area_visivel * p_esquerda)
#             corte_pixel_direita = int(largura_area_visivel * p_direita)
#             corte_pixel_cima = int(altura_area_visivel * p_cima)
#             corte_pixel_baixo = int(altura_area_visivel * p_baixo)
            
#             # Aplica o corte agressivo às coordenadas já calculadas pelo corte automático
#             nova_x1 = x1 + corte_pixel_esquerda
#             nova_y1 = y1 + corte_pixel_cima
#             nova_x2 = x2 - corte_pixel_direita
#             nova_y2 = y2 - corte_pixel_baixo

#             # Garante que as novas coordenadas não se invertam (mínimo 1x1 pixel)
#             if nova_x1 >= nova_x2:
#                 nova_x1 = (x1 + x2) // 2
#                 nova_x2 = nova_x1 + 1
#             if nova_y1 >= nova_y2:
#                 nova_y1 = (y1 + y2) // 2
#                 nova_y2 = nova_y1 + 1

#             # Atualiza as coordenadas finais de corte com os valores agressivos
#             x1, y1, x2, y2 = nova_x1, nova_y1, nova_x2, nova_y2

#         # 7. Aplica o Corte Final na imagem
#         # A imagem será cortada apenas se 'cortar_imagem' for True (seja corte automático ou agressivo)
#         if cortar_imagem and has_visible_pixel: 
#              # O +1 nos segundos argumentos é porque .crop() é exclusivo no final
#              result_image = result_image.crop((x1, y1, x2 + 1, y2 + 1))
#         # Se 'cortar_imagem' for False, a imagem permanece como foi retornada pelo rembg

#         # 8. Salva a imagem processada
#         result_image.save(caminho_imagem_saida)
        
#         status_corte = ""
#         if cortar_imagem:
#             status_corte = "(cortada"
#             if corte_agressivo and has_visible_pixel: # Só mostra detalhes do agressivo se foi aplicado
#                 p_c, p_d, p_b, p_e = [int(p * 100) for p in percentuais_norm]
#                 status_corte += f" agressivamente: Cima {p_c}%, Direita {p_d}%, Baixo {p_b}%, Esquerda {p_e}%)"
#             else:
#                 status_corte += " automaticamente)"
#         else:
#             status_corte = "(sem corte)"

#         print(f"✓ Fundo removido e imagem salva com sucesso em: {caminho_imagem_saida} {status_corte}")

#     except FileNotFoundError:
#         print(f"Erro: Arquivo de entrada '{caminho_imagem_entrada}' não encontrado.")
#     except Exception as e:
#         print(f"Erro ao processar e salvar a imagem: {e}")


# remover_fundo_e_salvar_com_corte("personagem/teste2.png", "personagem/saida_8.png", True,True,(0,0.2,0,0))