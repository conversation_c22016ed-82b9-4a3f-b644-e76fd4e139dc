from PIL import Image, ImageDraw
from rembg import remove
from io import BytesIO
from typing import Literal, <PERSON><PERSON>

def enquadrar_personagem_no_quadro(
    caminho_imagem_entrada: str,
    caminho_imagem_saida: str,
    dimensoes_quadro: Tuple[int, int] = (1920, 1080),
    lado: Literal["esquerda", "direita", "centro"] = "esquerda",
    margem_adicional: int = 0,
    cor_fundo: str = "black"
):
    """
    Detecta um personagem, redimensiona para ocupar toda a altura do quadro
    e o alinha horizontalmente conforme especificado.
    
    Argumentos:
        caminho_imagem_entrada (str): Caminho para o arquivo da imagem de entrada.
        caminho_imagem_saida (str): Caminho onde a imagem final será salva.
        dimensoes_quadro (tuple): (largura, altura) do quadro final. Ex: (1920, 1080)
        lado (str): "esquerda", "direita" ou "centro" - alinhamento horizontal.
        margem_adicional (int): Pixels adicionais de margem após o alinhamento.
                               Positivo = afasta da borda, Negativo = aproxima da borda.
        cor_fundo (str): Cor do fundo do quadro. Padrão: "black"
    """
    try:
        largura_quadro, altura_quadro = dimensoes_quadro
        
        # 1. Abrir a imagem original
        imagem_original = Image.open(caminho_imagem_entrada).convert("RGBA")
        
        # 2. Detectar o personagem usando rembg
        print("Detectando o personagem na imagem...")
        bytes_imagem_original = BytesIO()
        imagem_original.save(bytes_imagem_original, format='PNG')
        bytes_imagem_original.seek(0)
        bytes_sem_fundo = remove(bytes_imagem_original.read())
        imagem_mascara = Image.open(BytesIO(bytes_sem_fundo)).convert("RGBA")
        
        # 3. Encontrar a bounding box do personagem
        bbox = imagem_mascara.getbbox()
        if not bbox:
            print("Nenhum personagem detectado.")
            # Criar imagem vazia com as dimensões do quadro
            imagem_final = Image.new("RGB", dimensoes_quadro, cor_fundo)
            imagem_final.save(caminho_imagem_saida)
            return
        
        x1, y1, x2, y2 = bbox
        print(f"Personagem detectado: (x1={x1}, y1={y1}, x2={x2}, y2={y2})")
        
        # 4. Recortar o personagem da imagem original
        personagem_recortado = imagem_original.crop(bbox)
        largura_personagem = x2 - x1
        altura_personagem = y2 - y1
        
        # 5. Calcular o fator de redimensionamento
        # O personagem deve ocupar toda a altura do quadro
        fator_escala = altura_quadro / altura_personagem
        
        # Calcular as novas dimensões do personagem
        nova_largura = int(largura_personagem * fator_escala)
        nova_altura = altura_quadro
        
        print(f"Redimensionando personagem de {largura_personagem}x{altura_personagem} para {nova_largura}x{nova_altura}")
        
        # 6. Redimensionar o personagem
        personagem_redimensionado = personagem_recortado.resize(
            (nova_largura, nova_altura), 
            Image.Resampling.LANCZOS
        )
        
        # 7. Criar o quadro final com o fundo especificado
        quadro_final = Image.new("RGB", dimensoes_quadro, cor_fundo)
        
        # 8. Calcular a posição horizontal baseada no alinhamento
        if lado.lower() == "esquerda":
            pos_x = margem_adicional
        elif lado.lower() == "direita":
            pos_x = largura_quadro - nova_largura - margem_adicional
        elif lado.lower() == "centro":
            pos_x = (largura_quadro - nova_largura) // 2 + margem_adicional
        else:
            raise ValueError("O parâmetro 'lado' deve ser 'esquerda', 'direita' ou 'centro'")
        
        # Posição Y é sempre 0 (topo do quadro)
        pos_y = 0
        
        # 9. Verificar se o personagem cabe no quadro horizontalmente
        if nova_largura > largura_quadro:
            print(f"⚠️ Aviso: O personagem ({nova_largura}px) é mais largo que o quadro ({largura_quadro}px)")
            # Centralizar e cortar as extremidades
            excesso = nova_largura - largura_quadro
            corte_esquerda = excesso // 2
            corte_direita = excesso - corte_esquerda
            
            # Recortar o personagem para caber no quadro
            personagem_redimensionado = personagem_redimensionado.crop(
                (corte_esquerda, 0, nova_largura - corte_direita, nova_altura)
            )
            nova_largura = largura_quadro
            pos_x = 0  # Forçar posição 0 quando o personagem é cortado
        
        # 10. Ajustar se sai das bordas devido à margem
        if pos_x < 0:
            # Cortar a parte que sai pela esquerda
            pixels_a_cortar = abs(pos_x)
            if pixels_a_cortar < nova_largura:
                personagem_redimensionado = personagem_redimensionado.crop(
                    (pixels_a_cortar, 0, nova_largura, nova_altura)
                )
                nova_largura -= pixels_a_cortar
            pos_x = 0
        elif pos_x + nova_largura > largura_quadro:
            # Cortar a parte que sai pela direita
            pixels_excedentes = (pos_x + nova_largura) - largura_quadro
            if pixels_excedentes < nova_largura:
                personagem_redimensionado = personagem_redimensionado.crop(
                    (0, 0, nova_largura - pixels_excedentes, nova_altura)
                )
        
        # 11. Colar o personagem no quadro
        quadro_final.paste(personagem_redimensionado, (int(pos_x), int(pos_y)), personagem_redimensionado)
        
        # 12. Salvar o resultado
        quadro_final.save(caminho_imagem_saida)
        print(f"✓ Operação concluída! Quadro {largura_quadro}x{altura_quadro} com personagem alinhado à {lado}")
        print(f"✓ Imagem salva em: {caminho_imagem_saida}")
        
    except FileNotFoundError:
        print(f"Erro: Arquivo de entrada '{caminho_imagem_entrada}' não encontrado.")
    except Exception as e:
        print(f"Ocorreu um erro inesperado: {e}")


def alinhar_personagem_lateral(
    caminho_imagem_entrada: str,
    caminho_imagem_saida: str,
    lado: Literal["esquerda", "direita"] = "esquerda",
    margem_adicional: int = 0
):
    """
    Função original mantida para compatibilidade.
    Alinha o personagem nas laterais mantendo suas dimensões originais.
    """
    try:
        imagem_original = Image.open(caminho_imagem_entrada).convert("RGBA")
        largura, altura = imagem_original.size

        print("Detectando o personagem na imagem...")
        bytes_imagem_original = BytesIO()
        imagem_original.save(bytes_imagem_original, format='PNG')
        bytes_imagem_original.seek(0)
        bytes_sem_fundo = remove(bytes_imagem_original.read())
        imagem_mascara = Image.open(BytesIO(bytes_sem_fundo)).convert("RGBA")

        bbox = imagem_mascara.getbbox()
        if not bbox:
            print("Nenhum personagem detectado. Salvando imagem original.")
            imagem_original.save(caminho_imagem_saida)
            return

        x1, y1, x2, y2 = bbox
        print(f"Personagem detectado na área: (x1={x1}, y1={y1}, x2={x2}, y2={y2})")
        
        personagem_recortado = imagem_original.crop(bbox)
        largura_personagem = x2 - x1
        altura_personagem = y2 - y1

        imagem_final = imagem_original.copy()
        draw = ImageDraw.Draw(imagem_final)
        draw.rectangle(bbox, fill="black")

        if lado.lower() == "esquerda":
            pos_x_final = margem_adicional
        elif lado.lower() == "direita":
            pos_x_final = largura - largura_personagem - margem_adicional
        else:
            raise ValueError("O parâmetro 'lado' deve ser 'esquerda' ou 'direita'")
        
        pos_y_final = y1
        
        personagem_a_colar = personagem_recortado
        
        if pos_x_final < 0:
            pixels_a_cortar = abs(pos_x_final)
            if pixels_a_cortar < largura_personagem:
                personagem_a_colar = personagem_recortado.crop(
                    (pixels_a_cortar, 0, largura_personagem, altura_personagem)
                )
            pos_x_final = 0
        
        elif pos_x_final + largura_personagem > largura:
            pixels_excedentes = (pos_x_final + largura_personagem) - largura
            nova_largura = largura_personagem - pixels_excedentes
            if nova_largura > 0:
                personagem_a_colar = personagem_recortado.crop(
                    (0, 0, nova_largura, altura_personagem)
                )

        imagem_final.paste(personagem_a_colar, (int(pos_x_final), int(pos_y_final)))
        imagem_final.convert("RGB").save(caminho_imagem_saida)
        print(f"✓ Operação concluída! Personagem alinhado à {lado} com margem de {margem_adicional}px")
        print(f"✓ Imagem salva em: {caminho_imagem_saida}")

    except FileNotFoundError:
        print(f"Erro: Arquivo de entrada '{caminho_imagem_entrada}' não encontrado.")
    except Exception as e:
        print(f"Ocorreu um erro inesperado: {e}")


# --- EXEMPLOS DE USO ---
# if __name__ == '__main__':
#     entrada = r"C:\Users\<USER>\Desktop\projetos python\projetos_por_codigo\projetos\01_projeto_vingancas_avos\2025_06_25_23_43_13\video_0\video_pt\imagem_thumbnail\busca_imagens\imagem_thumbnail_0.jpg"

#     print("=== EXEMPLOS COM ENQUADRAMENTO EM DIMENSÕES ESPECÍFICAS ===\n")
    
#     # Exemplo 1: Enquadrar em Full HD (1920x1080) alinhado à esquerda
#     print("--- Enquadrando em Full HD (1920x1080) - ESQUERDA ---")
#     enquadrar_personagem_no_quadro(
#         caminho_imagem_entrada=entrada,
#         caminho_imagem_saida="resultado_fullhd_esquerda.jpg",
#         dimensoes_quadro=(1920, 1080),
#         lado="esquerda",
#         margem_adicional=50
#     )
    
#     # Exemplo 2: Enquadrar em Full HD alinhado à direita
#     print("\n--- Enquadrando em Full HD (1920x1080) - DIREITA ---")
#     enquadrar_personagem_no_quadro(
#         caminho_imagem_entrada=entrada,
#         caminho_imagem_saida="resultado_fullhd_direita.jpg",
#         dimensoes_quadro=(1920, 1080),
#         lado="direita",
#         margem_adicional=50
#     )
    
#     # Exemplo 3: Enquadrar em Full HD centralizado
#     print("\n--- Enquadrando em Full HD (1920x1080) - CENTRO ---")
#     enquadrar_personagem_no_quadro(
#         caminho_imagem_entrada=entrada,
#         caminho_imagem_saida="resultado_fullhd_centro.jpg",
#         dimensoes_quadro=(1920, 1080),
#         lado="centro",
#         margem_adicional=0
#     )
    
#     # Exemplo 4: Enquadrar em 4K (3840x2160)
#     print("\n--- Enquadrando em 4K (3840x2160) - ESQUERDA ---")
#     enquadrar_personagem_no_quadro(
#         caminho_imagem_entrada=entrada,
#         caminho_imagem_saida="resultado_4k_esquerda.jpg",
#         dimensoes_quadro=(3840, 2160),
#         lado="esquerda",
#         margem_adicional=100
#     )
    
#     # Exemplo 5: Enquadrar em formato quadrado (1080x1080) para Instagram
#     print("\n--- Enquadrando em Quadrado (1080x1080) - CENTRO ---")
#     enquadrar_personagem_no_quadro(
#         caminho_imagem_entrada=entrada,
#         caminho_imagem_saida="resultado_instagram_quadrado.jpg",
#         dimensoes_quadro=(1080, 1080),
#         lado="centro",
#         margem_adicional=0
#     )
    
#     # Exemplo 6: Enquadrar em formato vertical (1080x1920) para Stories/Reels
#     print("\n--- Enquadrando em Vertical (1080x1920) - CENTRO ---")
#     enquadrar_personagem_no_quadro(
#         caminho_imagem_entrada=entrada,
#         caminho_imagem_saida="resultado_stories_vertical.jpg",
#         dimensoes_quadro=(1080, 1920),
#         lado="centro",
#         margem_adicional=0
#     )
    
#     # Exemplo 7: Com fundo colorido personalizado
#     print("\n--- Enquadrando com fundo azul escuro ---")
#     enquadrar_personagem_no_quadro(
#         caminho_imagem_entrada=entrada,
#         caminho_imagem_saida="resultado_fundo_azul.jpg",
#         dimensoes_quadro=(1920, 1080),
#         lado="direita",
#         margem_adicional=30,
#         cor_fundo="#001F3F00"  # Azul escuro
#     )