import openai
import anthropic
import google.generativeai as genai
from google.api_core import exceptions as google_exceptions
import types
import time
import requests
import os
import traceback


# def chamar_openai(modelo, instrucoes, prompt, chave_api, chamador, temperatura=0.7, maximo_tokens=2000):
#     try:
#         client = openai.OpenAI(api_key=chave_api)

#         response = client.chat.completions.create(
#             model=modelo,
#             messages=[
#                 {"role": "system", "content": instrucoes},
#                 {"role": "user", "content": prompt}
#             ],
#             temperature=temperatura,
#             max_tokens=maximo_tokens
#         )
        
#         conteudo = response.choices[0].message.content
#         tokens_input = response.usage.prompt_tokens
#         tokens_output = response.usage.completion_tokens

#         return conteudo, tokens_input, tokens_output

#     except Exception as e:
#         raise ValueError(f"[{chamador}] Erro ao chamar modelo OpenAI '{modelo}': {str(e)}")


def chamar_openai(modelo, instrucoes, prompt, chave_api, chamador, temperatura=0.7, maximo_tokens=2000, agent_id=None):
    try:
        client = openai.OpenAI(api_key=chave_api)

        if agent_id:
            # Modo Agent (Assistant API)
            thread = client.beta.threads.create()

            client.beta.threads.messages.create(
                thread_id=thread.id,
                role="user",
                content=prompt
            )

            run = client.beta.threads.runs.create(
                thread_id=thread.id,
                assistant_id=agent_id
            )

            # Polling até o run finalizar
            while True:
                run_status = client.beta.threads.runs.retrieve(thread_id=thread.id, run_id=run.id)
                if run_status.status in ["completed", "failed", "cancelled"]:
                    break
                time.sleep(1)  # Evita excesso de requisições

            if run_status.status != "completed":
                raise ValueError(f"[{chamador}] O Agent terminou com status: {run_status.status}")

            messages = client.beta.threads.messages.list(thread_id=thread.id)
            resposta = messages.data[0].content[0].text.value

            tokens_input = None
            tokens_output = None  # Não temos usage direto na Assistants API no momento

            return resposta, tokens_input, tokens_output

        else:
            # Modo normal (chat completion)
            response = client.chat.completions.create(
                model=modelo,
                messages=[
                    {"role": "system", "content": instrucoes},
                    {"role": "user", "content": prompt}
                ],
                temperature=temperatura,
                max_tokens=maximo_tokens
            )
            
            conteudo = response.choices[0].message.content
            tokens_input = response.usage.prompt_tokens
            tokens_output = response.usage.completion_tokens

            return conteudo, tokens_input, tokens_output

    except Exception as e:
        raise ValueError(f"[{chamador}] Erro na chamada OpenAI: {str(e)}")


def chamar_claude(modelo, instrucoes, prompt, chave_api, chamador, temperatura=0.7, maximo_tokens=2000):
    try:
        client = anthropic.Anthropic(api_key=chave_api)

        resposta_completa = ""
        tokens_input = 0
        tokens_output = 0

        message = client.messages.create(
            model=modelo,
            max_tokens=maximo_tokens,
            temperature=temperatura,
            system=instrucoes,
            messages=[{"role": "user", "content": prompt}],
            stream=True
        )

        for chunk in message:
            if chunk.type == "content_block_delta":
                resposta_completa += chunk.delta.text or ""
            if chunk.type == "message_delta" and hasattr(chunk, "usage"):
                usage = chunk.usage
                tokens_input = getattr(usage, 'input_tokens', 0)
                tokens_output = getattr(usage, 'output_tokens', 0)

        return resposta_completa.strip(), tokens_input, tokens_output

    except Exception as e:
        raise ValueError(f"[{chamador}] Erro no Claude ({modelo}): {str(e)}")


def chamar_gemini(modelo, instrucoes, prompt, chave_api, chamador, temperatura=0.7, maximo_tokens=32000):
    try:
        genai.configure(api_key=chave_api)
        model = genai.GenerativeModel(modelo)

        # Combine instruções e prompt no texto do user
        texto_completo = (instrucoes.strip() + "\n" + prompt.strip()) if instrucoes else prompt.strip()

        response = model.generate_content(
            contents=[
                {
                    "role": "user",
                    "parts": [{"text": texto_completo}]
                }
            ],
            generation_config={
                "temperature": temperatura,
                "max_output_tokens": maximo_tokens
            }
        )

        conteudo = ""
        if response.candidates:
            for cand in response.candidates:
                if cand.content.parts:
                    for part in cand.content.parts:
                        if part.text:
                            conteudo += part.text

        tokens_input = response.usage_metadata.prompt_token_count if hasattr(response, "usage_metadata") else 0
        tokens_output = response.usage_metadata.candidates_token_count if hasattr(response, "usage_metadata") else 0

        return conteudo.strip(), tokens_input, tokens_output

    except Exception as e:
        raise ValueError(f"[{chamador}] Erro no Gemini ({modelo}): {str(e)}")


def chamar_openrouter(modelo, instrucoes, prompt, chave_api, chamador, temperatura=0.7, maximo_tokens=2000):
    try:
        client = openai.OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=chave_api
        )

        completion = client.chat.completions.create(
            model=modelo,
            messages=[
                {"role": "system", "content": instrucoes},
                {"role": "user", "content": prompt}
            ],
            temperature=temperatura,
            max_tokens=maximo_tokens,
            extra_headers={
                "HTTP-Referer": "https://seuprojeto.exemplo",
                "X-Title": "Meu Projeto com OpenRouter"
            }
        )

        conteudo = completion.choices[0].message.content
        tokens_input = completion.usage.prompt_tokens
        tokens_output = completion.usage.completion_tokens

        return conteudo, tokens_input, tokens_output

    except Exception as e:
        raise ValueError(f"[{chamador}] Erro no OpenRouter ({modelo}): {str(e)}")


def chamar_arcee(modelo, instrucoes, prompt, chave_api, chamador, temperatura=0.7, maximo_tokens=2000):
    try:
        client = openai.OpenAI(
            base_url="https://conductor.arcee.ai/v1",
            api_key=chave_api
        )

        completion = client.chat.completions.create(
            model=modelo,
            messages=[
                {"role": "system", "content": instrucoes},
                {"role": "user", "content": prompt}
            ],
            temperature=temperatura,
            # max_tokens=maximo_tokens
        )

        conteudo = completion.choices[0].message.content
        tokens_input = completion.usage.prompt_tokens
        tokens_output = completion.usage.completion_tokens

        return conteudo, tokens_input, tokens_output

    except Exception as e:
        raise ValueError(f"[{chamador}] Erro na arcee ({modelo}): {str(e)}")
    

def chamar_pollinations(modelo, instrucoes, prompt, chamador, temperatura=0.7, maximo_tokens=2000):
    """
    Faz uma chamada ao endpoint da Pollinations usando requests e retorna o conteúdo gerado.
    
    Args:
        modelo (str): Nome do modelo (ex: 'openai-large').
        instrucoes (str): Texto do system.
        prompt (str): Texto do user.
        chamador (str): Nome identificador para logs.
        temperatura (float): Temperatura da geração.
        maximo_tokens (int): Máximo de tokens na resposta.
    
    Returns:
        str: Conteúdo gerado pelo modelo.
    """
    url = "https://text.pollinations.ai/openai"
    
    payload = {
        "model": modelo,
        "messages": [
            {"role": "system", "content": instrucoes},
            {"role": "user", "content": prompt}
        ],
        "temperature": temperatura,
        "max_tokens": maximo_tokens
    }
    
    try:
        response = requests.post(url, json=payload, timeout=60)
        response.raise_for_status()
        data = response.json()

        # Extrair o conteúdo
        conteudo = data['choices'][0]['message']['content']

        # Se quiser, pode extrair os tokens (opcional)
        tokens_input = data.get('usage', {}).get('prompt_tokens', 0)
        tokens_output = data.get('usage', {}).get('completion_tokens', 0)

        print(f"[{chamador}] ✅ Gerado com sucesso — Tokens: in={tokens_input} out={tokens_output}")
        
        return conteudo, 0 ,0 
    
    except Exception as e:
        raise ValueError(f"[{chamador}] ❌ Erro na Pollinations ({modelo}): {str(e)}")


def executar_ai_texto(servico, modelo, instrucoes, prompt, chave_api, chamador, temperatura=0.7, maximo_tokens=2000, agent_id=None):
    from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeout
    
    start_time = time.time()
    timeout = 1200  # 20 minutos

    def executar_chamada():
        match servico.lower():
            case "openai":
                return chamar_openai(modelo, instrucoes, prompt, chave_api, chamador, temperatura, maximo_tokens, agent_id)
            case "anthropic":
                return chamar_claude(modelo, instrucoes, prompt, chave_api, chamador, temperatura, maximo_tokens)
            case "google":
                return chamar_gemini(modelo, instrucoes, prompt, chave_api, chamador, temperatura, maximo_tokens)
            case "openrouter":
                return chamar_openrouter(modelo, instrucoes, prompt, chave_api, chamador, temperatura, maximo_tokens)
            case "arcee":
                return chamar_arcee(modelo, instrucoes, prompt, chave_api, chamador, temperatura, maximo_tokens)
            case "pollinations":
                return chamar_pollinations(modelo, instrucoes, prompt, chave_api, temperatura, maximo_tokens)
            case _:
                raise ValueError(f"[{chamador}] Serviço '{servico}' não reconhecido.")

    try:
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(executar_chamada)
            resultado = future.result(timeout=timeout)
            return resultado[0]

    except FutureTimeout:
        future.cancel()
        tempo_decorrido = time.time() - start_time
        raise ValueError(f"[{chamador}] A operação demorou mais de 20 minutos (tempo decorrido: {tempo_decorrido / 60:.1f} minutos)")

    except Exception as e:
        if "rate limit" in str(e).lower() or "quota" in str(e).lower():
            tempo_decorrido = time.time() - start_time
            tempo_restante = timeout - tempo_decorrido
            if tempo_restante > 60:
                time.sleep(60)
                return executar_ai_texto(servico, modelo, instrucoes, prompt, chave_api, chamador, temperatura, maximo_tokens)
        traceback.print_exc()
        raise ValueError(f"[{chamador}] Erro ao executar AI: {str(e)}")
