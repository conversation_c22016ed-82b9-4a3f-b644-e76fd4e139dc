import requests

url = "https://text.pollinations.ai/openai"
headers = {
    "Content-Type": "application/json"
}

data = {
    "model": "openai",
    "messages": [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "What is in this image?"},
                {"type": "image_url", "image_url": {
                    "url": "https://padilhaautomatizacoes.blob.core.windows.net/audiotts/teste_thumb.jpg"
                }}
            ]
        }
    ],
    "max_tokens": 300
}

response = requests.post(url, headers=headers, json=data)

# Exibe o resultado formatado
print("Status code:", response.status_code)
print("Resposta:")
print(response.json())
