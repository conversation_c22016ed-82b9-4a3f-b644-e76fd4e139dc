#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Organizador Final - Compacta vídeos finalizados com thumbnails e títulos
"""

import os
import zipfile
import shutil
from datetime import datetime


def verificar_ou_criar_pasta(caminho):
    """Cria pasta se não existir"""
    if not os.path.exists(caminho):
        os.makedirs(caminho)
    return caminho


def listar_arquivos(pasta, extensoes=None):
    """Lista arquivos de uma pasta com extensões específicas"""
    if not os.path.exists(pasta):
        return []
    
    arquivos = []
    for arquivo in os.listdir(pasta):
        caminho_completo = os.path.join(pasta, arquivo)
        if os.path.isfile(caminho_completo):
            if extensoes is None:
                arquivos.append(arquivo)
            else:
                if any(arquivo.lower().endswith(ext.lower()) for ext in extensoes):
                    arquivos.append(arquivo)
    return arquivos


def encontrar_video_final(pasta_renderizacao):
    """Encontra o vídeo final renderizado"""
    pasta_video_final = os.path.join(pasta_renderizacao, '100_video_renderizado')
    if os.path.exists(pasta_video_final):
        videos = listar_arquivos(pasta_video_final, ['.mp4', '.avi', '.mov'])
        if videos:
            return os.path.join(pasta_video_final, videos[0])
    return None


def encontrar_thumbnail(pasta_video):
    """Encontra a thumbnail correta (thumbnail_1)"""
    # Procurar especificamente por thumbnail_1 na pasta principal
    thumbnails = listar_arquivos(pasta_video, ['.png', '.jpg', '.jpeg'])
    for thumb in thumbnails:
        if 'thumbnail_1' in thumb.lower():
            return os.path.join(pasta_video, thumb)


    return None


def encontrar_titulo(pasta_video):
    """Encontra o arquivo de título"""
    arquivo_titulo = os.path.join(pasta_video, '0_titulo.txt')
    if os.path.exists(arquivo_titulo):
        return arquivo_titulo
    return None


def ler_titulo(caminho_titulo):
    """Lê o conteúdo do arquivo de título"""
    try:
        with open(caminho_titulo, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except:
        return "Título não disponível"


def criar_nome_arquivo_limpo(pasta_video, idioma):
    """Cria um nome de arquivo baseado no nome da pasta original"""
    # Pegar o nome da pasta (ex: 1_video_pt -> 1_video_pt)
    nome_pasta = os.path.basename(pasta_video)
    return f"{nome_pasta}_pronto"


def organizar_arquivos_finais(pasta_video, idioma, forcar_recriar=False):
    """
    Organiza e compacta os arquivos finais de um vídeo em um idioma específico

    Args:
        pasta_video: Caminho para a pasta do vídeo (ex: 1_video_pt)
        idioma: Código do idioma (ex: pt, en, es)
        forcar_recriar: Se True, recria o ZIP mesmo se já existir

    Returns:
        str: Caminho do arquivo ZIP criado ou None se houver erro
    """
    
    print(f"📦 Organizando arquivos finais para idioma: {idioma}")
    
    # Verificar se a pasta existe
    if not os.path.exists(pasta_video):
        print(f"❌ Pasta não encontrada: {pasta_video}")
        return None
    
    # Encontrar arquivos necessários
    pasta_renderizacao = os.path.join(pasta_video, 'renderizacao')
    video_final = encontrar_video_final(pasta_renderizacao)
    thumbnail = encontrar_thumbnail(pasta_video)
    arquivo_titulo = encontrar_titulo(pasta_video)
    
    # Verificar se encontrou os arquivos essenciais
    if not video_final:
        print(f"❌ Vídeo final não encontrado em: {pasta_renderizacao}")
        return None
    
    if not arquivo_titulo:
        print(f"❌ Arquivo de título não encontrado em: {pasta_video}")
        return None
    
    # Ler título e criar nome do arquivo baseado na pasta
    titulo = ler_titulo(arquivo_titulo)
    nome_arquivo = criar_nome_arquivo_limpo(pasta_video, idioma)
    
    # Criar pasta de saída
    pasta_saida = os.path.join(pasta_video, 'arquivos_finais')
    verificar_ou_criar_pasta(pasta_saida)
    
    # Nome do arquivo ZIP
    nome_zip = f"{nome_arquivo}.zip"
    caminho_zip = os.path.join(pasta_saida, nome_zip)

    # Verificar se o ZIP já existe e está válido (a menos que seja forçada a recriação)
    if os.path.exists(caminho_zip) and not forcar_recriar:
        try:
            # Verificar se o ZIP é válido e contém os arquivos esperados
            with zipfile.ZipFile(caminho_zip, 'r') as zip_test:
                arquivos_no_zip = zip_test.namelist()

                # Verificar se contém pelo menos o vídeo
                video_esperado = f"{nome_arquivo}.mp4"
                if video_esperado in arquivos_no_zip:
                    print(f"✅ Arquivo ZIP já existe e é válido: {nome_zip}")
                    print(f"   Contém: {len(arquivos_no_zip)} arquivo(s)")
                    print(f"   Pulando criação (arquivo já processado)")
                    return caminho_zip
                else:
                    print(f"⚠️  ZIP existe mas está incompleto: {nome_zip}")
                    print(f"   Recriando arquivo...")
        except (zipfile.BadZipFile, Exception) as e:
            print(f"⚠️  ZIP corrompido detectado: {nome_zip}")
            print(f"   Erro: {e}")
            print(f"   Recriando arquivo...")
    elif os.path.exists(caminho_zip) and forcar_recriar:
        print(f"🔄 Forçando recriação do ZIP: {nome_zip}")

    print(f"📁 Criando arquivo: {nome_zip}")

    try:
        with zipfile.ZipFile(caminho_zip, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            
            # Adicionar vídeo final
            nome_video_no_zip = f"{nome_arquivo}.mp4"
            zip_file.write(video_final, nome_video_no_zip)
            print(f"✅ Adicionado: {nome_video_no_zip}")

            # Adicionar thumbnail (se encontrada)
            if thumbnail:
                extensao_thumb = os.path.splitext(thumbnail)[1]
                nome_thumb_no_zip = f"{nome_arquivo}_thumbnail{extensao_thumb}"
                zip_file.write(thumbnail, nome_thumb_no_zip)
                print(f"✅ Adicionado: {nome_thumb_no_zip}")
            else:
                print("⚠️  Thumbnail não encontrada (procurando por thumbnail_1)")

            # Adicionar título
            nome_titulo_no_zip = f"{nome_arquivo}_titulo.txt"
            zip_file.write(arquivo_titulo, nome_titulo_no_zip)
            print(f"✅ Adicionado: {nome_titulo_no_zip}")
            
            # Adicionar arquivo de informações
            info_content = f"""INFORMAÇÕES DO VÍDEO
========================

Título: {titulo}
Idioma: {idioma.upper()}
Data de criação: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
Pasta original: {os.path.basename(pasta_video)}

CONTEÚDO DO ARQUIVO:
- {nome_video_no_zip}: Vídeo final renderizado
- {nome_thumb_no_zip if thumbnail else 'thumbnail_1 não encontrada'}: Thumbnail do vídeo (thumbnail_1)
- {nome_titulo_no_zip}: Arquivo com o título
- info.txt: Este arquivo de informações

OBSERVAÇÕES:
- Nome do arquivo baseado na pasta original
- Thumbnail procurada especificamente: thumbnail_1.png
- Arquivo pronto para distribuição
"""
            
            zip_file.writestr("info.txt", info_content)
            print("✅ Adicionado: info.txt")
    
        print(f"🎉 Arquivo ZIP criado com sucesso: {caminho_zip}")
        
        # Mostrar tamanho do arquivo
        tamanho_mb = os.path.getsize(caminho_zip) / (1024 * 1024)
        print(f"📊 Tamanho do arquivo: {tamanho_mb:.2f} MB")
        
        return caminho_zip
        
    except Exception as e:
        print(f"❌ Erro ao criar arquivo ZIP: {e}")
        return None


def organizar_todos_idiomas(pasta_rodada, forcar_recriar=False):
    """
    Organiza arquivos finais para todos os idiomas de todos os vídeos

    Args:
        pasta_rodada: Pasta principal da rodada (ex: 2025_07_08_22_21_06)
        forcar_recriar: Se True, recria todos os ZIPs mesmo se já existirem
    """
    
    print("🚀 Iniciando organização de arquivos finais para todos os idiomas...")
    
    if not os.path.exists(pasta_rodada):
        print(f"❌ Pasta da rodada não encontrada: {pasta_rodada}")
        return
    
    # Listar todas as pastas de vídeos (video_1, video_2, etc.)
    pastas_videos = []
    for item in os.listdir(pasta_rodada):
        caminho_item = os.path.join(pasta_rodada, item)
        if os.path.isdir(caminho_item) and item.startswith('video_'):
            pastas_videos.append(caminho_item)
    
    if not pastas_videos:
        print("❌ Nenhuma pasta de vídeo encontrada")
        return
    
    total_zips_criados = 0
    
    for pasta_video in pastas_videos:
        print(f"\n📂 Processando: {os.path.basename(pasta_video)}")
        
        # Listar todas as subpastas de idiomas
        subpastas_idiomas = []
        for item in os.listdir(pasta_video):
            caminho_subpasta = os.path.join(pasta_video, item)
            if os.path.isdir(caminho_subpasta) and '_video_' in item:
                subpastas_idiomas.append(caminho_subpasta)
        
        for pasta_idioma in subpastas_idiomas:
            nome_pasta = os.path.basename(pasta_idioma)
            # Extrair código do idioma (ex: 1_video_pt -> pt)
            idioma = nome_pasta.split('_')[-1]
            
            print(f"\n  🌍 Processando idioma: {idioma}")
            
            zip_criado = organizar_arquivos_finais(pasta_idioma, idioma, forcar_recriar)
            if zip_criado:
                total_zips_criados += 1
    
    print(f"\n🎉 Organização concluída!")
    print(f"📦 Total de arquivos ZIP criados: {total_zips_criados}")


# Teste da função (se executado diretamente)
if __name__ == "__main__":
    # Exemplo de uso
    pasta_teste = r"C:\Users\<USER>\OneDrive\Desktop\projeto\projetos\01_projeto_vingancas_avos\2025_07_08_22_21_06"
    if os.path.exists(pasta_teste):
        organizar_todos_idiomas(pasta_teste)
    else:
        print("Pasta de teste não encontrada. Ajuste o caminho no código.")
