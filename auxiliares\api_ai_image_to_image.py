import requests
import urllib.parse
import time
import base64
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed


# 🗓️ Gera timestamp (caso queira usar para nomeação)
def gerar_timestamp():
    return datetime.now().strftime("%Y%m%d_%H%M%S")


# 🖼️ Converte bytes para string base64
def converter_para_base64(image_bytes):
    return base64.b64encode(image_bytes).decode("utf-8")


# 🎨 Geração Pollinations com chamadas paralelas para simular quantidade
def gerar_imagem_pollinations(prompt, modelo="flux", nologo=True, private=True, enhance=False, safe=True, quantidade=1):
    def gerar_uma_imagem(index):
        encoded_prompt = urllib.parse.quote(prompt)
        url = f"https://image.pollinations.ai/prompt/{encoded_prompt}"

        params = {
            "width": 1280,
            "height": 720,
            "model": modelo,
            "nologo": str(nologo).lower(),
            "private": str(private).lower(),
            "enhance": str(enhance).lower(),
            "safe": str(safe).lower(),
        }

        for tentativa in range(3):
            try:
                response = requests.get(url, params=params, timeout=300)
                response.raise_for_status()
                return converter_para_base64(response.content)
            except Exception as e:
                if tentativa < 2:
                    print(f"[Pollinations:{index}] Tentativa {tentativa + 1} falhou. Retentando após 40s...")
                    time.sleep(40)
                else:
                    raise Exception(f"[Pollinations:{index}] Erro após 3 tentativas: {e}")

    imagens_base64 = []

    with ThreadPoolExecutor(max_workers=min(quantidade, 10)) as executor:
        futures = {executor.submit(gerar_uma_imagem, i): i for i in range(quantidade)}
        for future in as_completed(futures):
            imagens_base64.append(future.result())

    return imagens_base64


# 🎨 Geração MiniMax (nativo com múltiplas imagens)
# def gerar_imagem_minimax(prompt, chave_api, modelo, n=1):
#     url = "https://api.minimaxi.chat/v1/image_generation"

#     payload = {
#         "model": modelo,
#         "prompt": prompt,
#         "aspect_ratio": "16:9",
#         "response_format": "url",
#         "n": n,
#         "prompt_optimizer": True
#     }

#     headers = {
#         'Authorization': f'Bearer {chave_api}',
#         'Content-Type': 'application/json'
#     }

#     for tentativa in range(3):
#         try:
#             response = requests.post(url, json=payload, headers=headers, timeout=300)
#             response.raise_for_status()

#             data = response.json()

#             if data['base_resp']['status_code'] != 0:
#                 raise Exception(f"Erro MiniMax: {data['base_resp']['status_msg']}")

#             imagens_base64 = []

#             for img_url in data["data"]["image_urls"]:
#                 img_response = requests.get(img_url, timeout=300)
#                 img_response.raise_for_status()

#                 image_base64 = converter_para_base64(img_response.content)
#                 imagens_base64.append(image_base64)

#             return imagens_base64

#         except Exception as e:
#             if tentativa < 2:
#                 print(f"Tentativa {tentativa + 1} falhou no MiniMax. Retentando após 40s...")
#                 time.sleep(40)
#             else:
#                 raise Exception(f"Erro ao gerar imagem no MiniMax após 3 tentativas: {e}")


def converter_para_base64(conteudo_bytes):
    return base64.b64encode(conteudo_bytes).decode('utf-8')


def gerar_imagem_minimax(prompt, chave_api, modelo, imagem_referencia=None, n=1):
    url = "https://api.minimaxi.chat/v1/image_generation"

    payload = {
        "model": modelo,
        "prompt": prompt,
        "aspect_ratio": "16:9",
        "response_format": "url",
        "n": n,
        "prompt_optimizer": True
    }

    # Adiciona imagem de referência se fornecida
    if imagem_referencia:
        try:
            with open(imagem_referencia, "rb") as img_file:
                img_base64 = base64.b64encode(img_file.read()).decode('utf-8')
            payload["subject_reference"] = [
                {
                    "type": "character",
                    "image_file": f"data:image/jpeg;base64,{img_base64}"
                }
            ]
        except Exception as e:
            raise Exception(f"Erro ao ler imagem de referência: {e}")

    headers = {
        'Authorization': f'Bearer {chave_api}',
        'Content-Type': 'application/json'
    }

    for tentativa in range(3):
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=300)
            response.raise_for_status()

            data = response.json()

            if data['base_resp']['status_code'] != 0:
                raise Exception(f"Erro MiniMax: {data['base_resp']['status_msg']}")

            imagens_base64 = []

            for img_url in data["data"]["image_urls"]:
                img_response = requests.get(img_url, timeout=300)
                img_response.raise_for_status()

                image_base64 = converter_para_base64(img_response.content)
                imagens_base64.append(image_base64)

            return imagens_base64

        except Exception as e:
            if tentativa < 2:
                print(f"Tentativa {tentativa + 1} falhou no MiniMax. Retentando após 40s...")
                time.sleep(40)
            else:
                raise Exception(f"Erro ao gerar imagem no MiniMax após 3 tentativas: {e}")

# 🔧 Executor Geral
def executar_ai_imagem(servico, modelo, prompt, chave_api, chamador, quantidade):
    match servico.lower():
        case "pollinations":
            return gerar_imagem_pollinations(prompt, modelo=modelo, quantidade=quantidade)
        case "minimax":
            return gerar_imagem_minimax(prompt, chave_api, modelo, n=quantidade)
        case _:
            raise ValueError(f"[{chamador}] Serviço '{servico}' não reconhecido.")
