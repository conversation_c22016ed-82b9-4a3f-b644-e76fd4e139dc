from PIL import Image, ImageDraw, ImageFont, ImageOps, ImageEnhance, ImageFilter
import colorsys
import os
import re
import string

# --- Funções Auxiliares ---
def hex_to_rgba(hex_color):
    if not isinstance(hex_color, str):
        raise ValueError("O código hexadecimal de cor deve ser uma string.")
    hex_color = hex_color.lstrip('#')
    if len(hex_color) == 6:
        hex_color += 'FF'
    if len(hex_color) != 8:
        raise ValueError("O código hexadecimal de cor deve ter 6 ou 8 dígitos.")
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4, 6))

def interpolate_color_rgba(color1_rgba, color2_rgba, ratio):
    ratio = max(0.0, min(1.0, ratio))
    r = int(color1_rgba[0] + (color2_rgba[0] - color1_rgba[0]) * ratio)
    g = int(color1_rgba[1] + (color2_rgba[1] - color1_rgba[1]) * ratio)
    b = int(color1_rgba[2] + (color2_rgba[2] - color1_rgba[2]) * ratio)
    a = int(color1_rgba[3] + (color2_rgba[3] - color1_rgba[3]) * ratio)
    return (r, g, b, a)

def wrap_text_to_fit_improved(text, font, available_width):
    if available_width <= 0: return text.split('\n')
    wrapped_lines = []
    for paragraph in text.split('\n'):
        if paragraph.strip() == "":
            wrapped_lines.append("")
            continue
        words = paragraph.split(' ')
        current_line_words = []
        while words:
            word = words.pop(0)
            line_with_word = " ".join(current_line_words + [word])
            if font.getlength(line_with_word) <= available_width:
                current_line_words.append(word)
            else:
                if current_line_words: wrapped_lines.append(" ".join(current_line_words))
                if font.getlength(word) > available_width:
                    temp_word = ""
                    for char in word:
                        if font.getlength(temp_word + char) > available_width:
                            wrapped_lines.append(temp_word); temp_word = char
                        else: temp_word += char
                    current_line_words = [temp_word] if temp_word else []
                else: current_line_words = [word]
        if current_line_words: wrapped_lines.append(" ".join(current_line_words))
    return wrapped_lines

def get_text_dimensions(lines, font, line_spacing):
    if not lines: return 0, 0
    text_block_width = 0
    
    # Usa as métricas da fonte para uma altura de linha consistente
    ascent, descent = font.getmetrics()
    standard_line_height = ascent + descent
    
    for line in lines:
        line_width = font.getlength(line)
        if line_width > text_block_width:
            text_block_width = line_width
    
    # Calcula a altura total com base na altura padrão e no número de linhas
    text_block_height = (len(lines) * standard_line_height)
    if len(lines) > 1:
        text_block_height += (len(lines) - 1) * line_spacing
        
    return text_block_width, text_block_height

def draw_gradient_text(image, text, xy, font, s_color, e_color, direction='horizontal'):
    word_bbox = font.getbbox(text)
    word_width, word_height = int(font.getlength(text)), word_bbox[3] - word_bbox[1]
    if word_width <= 0 or word_height <= 0: return
    grad_img = Image.new('RGBA', (word_width, word_height))
    limit = word_width if direction == 'horizontal' else word_height
    for i in range(limit):
        ratio = i / (limit - 1) if limit > 1 else 0
        color = interpolate_color_rgba(s_color, e_color, ratio)
        if direction == 'horizontal': ImageDraw.Draw(grad_img).line([(i, 0), (i, word_height)], fill=color)
        else: ImageDraw.Draw(grad_img).line([(0, i), (word_width, i)], fill=color)
    mask = Image.new('L', (word_width, word_height)); ImageDraw.Draw(mask).text((-word_bbox[0], -word_bbox[1]), text, font=font, fill=255)
    image.paste(grad_img, (int(xy[0] + word_bbox[0]), int(xy[1] + word_bbox[1])), mask)

# --- FUNÇÃO PRINCIPAL ---
def generate_youtube_thumbnail(config):
    frame_width = config.get('frame', {}).get('width', 1920); frame_height = config.get('frame', {}).get('height', 1080)
    try: bg_color = hex_to_rgba(config.get('frame', {}).get('background_color', '#000000'))
    except (ValueError, TypeError): bg_color = (0, 0, 0, 255)
    thumbnail = Image.new('RGBA', (frame_width, frame_height), bg_color)
    for layer in config.get('layers', []):
        layer_type = layer.get('type')
        if layer_type in ['solid_color', 'image']:
            shape_img = None
            if layer_type == 'solid_color':
                if not ('color' in layer or 'gradient' in layer): continue
                shape_width = int(layer.get('width_percent', 1.0) * frame_width); shape_height = int(layer.get('height_percent', 1.0) * frame_height)
                shape_img = Image.new('RGBA', (shape_width, shape_height))
                if 'gradient' in layer:
                    grad = layer['gradient']
                    s_color, e_color = hex_to_rgba(grad['start_color']), hex_to_rgba(grad['end_color'])
                    start_percent, end_percent = grad.get('start_percent', 0.0), grad.get('end_percent', 1.0)
                    direction = grad.get('direction', 'left_to_right')
                    limit = shape_width if 'left' in direction or 'right' in direction else shape_height
                    for i in range(limit):
                        current_pos_ratio = i / (limit - 1) if limit > 1 else 0
                        if current_pos_ratio < start_percent: color = s_color
                        elif current_pos_ratio > end_percent: color = e_color
                        else:
                            grad_range = end_percent - start_percent
                            ratio_in_grad = (current_pos_ratio - start_percent) / grad_range if grad_range > 0 else 0
                            if direction == 'right_to_left' or direction == 'bottom_to_top':
                                ratio_in_grad = 1 - ratio_in_grad
                            color = interpolate_color_rgba(s_color, e_color, ratio_in_grad)
                        line_coords = [(i, 0), (i, shape_height)] if 'left' in direction or 'right' in direction else [(0, i), (shape_width, i)]
                        ImageDraw.Draw(shape_img).line(line_coords, fill=color)
                else:
                    base_color_rgba = hex_to_rgba(layer.get('color'))
                    ImageDraw.Draw(shape_img).rectangle([(0,0), (shape_width, shape_height)], fill=base_color_rgba)
            else: # type 'image'
                image_path = layer.get('path')
                if not image_path or not os.path.exists(image_path): continue
                try: shape_img = Image.open(image_path).convert("RGBA")
                except Exception: continue
                if 'resize_percent' in layer: shape_img = shape_img.resize((int(shape_img.width * layer['resize_percent']), int(shape_img.height * layer['resize_percent'])), Image.Resampling.LANCZOS)
                if 'brightness' in layer:
                    enhancer = ImageEnhance.Brightness(shape_img)
                    shape_img = enhancer.enhance(layer['brightness'])
            
            if shape_img is None: continue

            if 'opacity' in layer:
                if shape_img.mode != 'RGBA': shape_img = shape_img.convert('RGBA')
                alpha = shape_img.getchannel('A'); alpha = ImageEnhance.Brightness(alpha).enhance(layer['opacity']); shape_img.putalpha(alpha)
            
            initial_alignment = layer.get('initial_alignment', 'top_left')
            shape_width, shape_height = shape_img.size; x_pos, y_pos = 0, 0
            if initial_alignment == 'center': x_pos, y_pos = (frame_width-shape_width)//2, (frame_height-shape_height)//2
            elif initial_alignment == 'top_left': x_pos, y_pos = 0, 0
            elif initial_alignment == 'top_right': x_pos, y_pos = frame_width - shape_width, 0
            elif initial_alignment == 'bottom_left': x_pos, y_pos = 0, frame_height - shape_height
            elif initial_alignment == 'bottom_right': x_pos, y_pos = frame_width - shape_width, frame_height - shape_height
            margin = layer.get('margin', {}); x_pos += margin.get('left', 0) - margin.get('right', 0); y_pos += margin.get('top', 0) - margin.get('bottom', 0)
            thumbnail.paste(shape_img, (x_pos, y_pos), shape_img)
        
        elif layer_type == 'text':
            font_size = layer.get('font_size', 50); font_path = layer.get('font_path'); text_with_markers = layer.get('text', '')
            color_rules = layer.get('color_rules', {}); delimiters = [re.escape(key) for key in color_rules.keys()]
            pattern = re.compile(f"({'|'.join(delimiters)})(.*?)\\1", re.DOTALL) if delimiters else None
            clean_text = re.sub(pattern, r'\2', text_with_markers) if pattern else text_with_markers
            
            # Nova lógica para mapear frases inteiras
            phrase_map = {}
            if pattern:
                for match in pattern.finditer(text_with_markers):
                    delimiter = match.group(1)
                    full_phrase = match.group(2)
                    # Mapeia cada palavra da frase para o delimitador
                    words_in_phrase = full_phrase.split()
                    for word in words_in_phrase:
                        clean_word = word.strip(string.punctuation)
                        phrase_map[clean_word] = delimiter
            
            # NOVAS CONFIGURAÇÕES PARA CORES POR LINHA
            first_lines_config = layer.get('first_lines_color')  # {'count': 3, 'color': '#FF0000', 'remaining_color': '#0000FF'}
            alternating_lines_config = layer.get('alternating_lines')  # {'color1': '#FF0000', 'color2': '#0000FF'}
            
            box_width = layer.get('width', -1)
            if 'width_percent' in layer: box_width = int(frame_width * layer['width_percent'])
            box_height = layer.get('height', -1)
            if 'height_percent' in layer: box_height = int(frame_height * layer['height_percent'])
            padding = layer.get('padding', {}); padding_top, padding_bottom = padding.get('top', 0), padding.get('bottom', 0)
            padding_left, padding_right = padding.get('left', 0), padding.get('right', 0)
            available_width_for_text = max(0, box_width - padding_left - padding_right if box_width > 0 else frame_width)
            available_height_for_text = max(0, box_height - padding_top - padding_bottom if box_height > 0 else frame_height)
            if layer.get('auto_fit', False) and box_height > 0 and box_width > 0:
                min_font_size = layer.get('min_font_size', 20); temp_font_size = font_size
                while temp_font_size >= min_font_size:
                    try: font = ImageFont.truetype(font_path, temp_font_size)
                    except IOError: font = ImageFont.load_default()
                    final_text_lines = wrap_text_to_fit_improved(clean_text, font, available_width_for_text)
                    _, text_height = get_text_dimensions(final_text_lines, font, layer.get('line_spacing', 4))
                    if text_height <= available_height_for_text: break
                    temp_font_size -= 2
                font_size = max(temp_font_size, min_font_size)
            try: font = ImageFont.truetype(font_path, font_size)
            except IOError: font = ImageFont.load_default(size=font_size)
            final_text_lines = wrap_text_to_fit_improved(clean_text, font, available_width_for_text)
            text_block_width, text_block_height = get_text_dimensions(final_text_lines, font, layer.get('line_spacing', 4))
            canvas_width = int(box_width if box_width > 0 else text_block_width + padding_left + padding_right)
            canvas_height = int(box_height if box_height > 0 else text_block_height + padding_top + padding_bottom)
            if canvas_width <= 0 or canvas_height <= 0: continue
            
            final_text_img = Image.new('RGBA', (canvas_width, canvas_height))
            draw_final = ImageDraw.Draw(final_text_img)
            h_align, v_align = layer.get('horizontal_align', 'left'), layer.get('vertical_align', 'top')
            line_spacing = layer.get('line_spacing', 4)
            default_gradient = layer.get('gradient')
            default_color = hex_to_rgba(layer.get('text_color', '#FFFFFF'))
            highlighter_rects = []
            
            # Obtém a altura padrão da linha para garantir espaçamento uniforme.
            ascent, descent = font.getmetrics()
            standard_line_height = ascent + descent
            
            if v_align == 'middle': y_cursor = padding_top + (available_height_for_text - text_block_height) / 2
            elif v_align == 'bottom': y_cursor = padding_top + available_height_for_text - text_block_height
            else: y_cursor = padding_top
            
            # LOOP PRINCIPAL DAS LINHAS COM NOVAS LÓGICAS DE COR
            for line_index, line in enumerate(final_text_lines):
                line_width = font.getlength(line)
                
                if h_align == 'center': x_cursor = padding_left + (available_width_for_text - line_width) / 2
                elif h_align == 'right': x_cursor = padding_left + available_width_for_text - line_width
                else: x_cursor = padding_left
                
                # DETERMINA A COR/GRADIENTE DA LINHA BASEADA NAS NOVAS CONFIGURAÇÕES
                line_color = default_color  # Cor padrão
                line_gradient = None  # Gradiente da linha
                
                if first_lines_config:
                    # Lógica das primeiras N linhas
                    first_count = first_lines_config.get('count', 3)
                    if line_index < first_count:
                        if 'gradient' in first_lines_config:
                            line_gradient = first_lines_config['gradient']
                        else:
                            line_color = hex_to_rgba(first_lines_config.get('color', '#FFFFFF'))
                    else:
                        if 'remaining_gradient' in first_lines_config:
                            line_gradient = first_lines_config['remaining_gradient']
                        else:
                            line_color = hex_to_rgba(first_lines_config.get('remaining_color', '#CCCCCC'))
                
                elif alternating_lines_config:
                    # Lógica alternada entre linhas
                    if line_index % 2 == 0:
                        if 'gradient1' in alternating_lines_config:
                            line_gradient = alternating_lines_config['gradient1']
                        else:
                            line_color = hex_to_rgba(alternating_lines_config.get('color1', '#FFFFFF'))
                    else:
                        if 'gradient2' in alternating_lines_config:
                            line_gradient = alternating_lines_config['gradient2']
                        else:
                            line_color = hex_to_rgba(alternating_lines_config.get('color2', '#CCCCCC'))
                
                # RENDERIZA CADA PALAVRA DA LINHA
                for word in line.split(' '):
                    clean_word = word.strip(string.punctuation)
                    delimiter = phrase_map.get(clean_word)
                    rule = color_rules.get(delimiter) if delimiter else None
                    
                    # Verifica se a palavra tem formatação especial (delimitadores)
                    if rule and 'highlighter' in rule:
                        hl = rule['highlighter']; px, py = hl.get('padding_x', 5), hl.get('padding_y', 2)
                        word_bbox = font.getbbox(word)
                        hl_x1_rel = x_cursor + word_bbox[0] - px; hl_y1_rel = y_cursor + word_bbox[1] - py
                        hl_x2_rel = hl_x1_rel + (word_bbox[2] - word_bbox[0]) + (px * 2); hl_y2_rel = hl_y1_rel + (word_bbox[3] - word_bbox[1]) + (py * 2)
                        highlighter_rects.append({"rect": [(hl_x1_rel, hl_y1_rel), (hl_x2_rel, hl_y2_rel)], "color": hex_to_rgba(hl['color'])})
                    
                    if rule and 'gradient' in rule:
                        grad = rule['gradient']; s_color, e_color = hex_to_rgba(grad['start_color']), hex_to_rgba(grad['end_color'])
                        draw_gradient_text(final_text_img, word, (x_cursor, y_cursor), font, s_color, e_color, grad.get('direction', 'horizontal'))
                    elif rule and 'color' in rule:
                        draw_final.text((x_cursor, y_cursor), word, font=font, fill=hex_to_rgba(rule['color']))
                    elif default_gradient:
                        scope = default_gradient.get('scope', 'word'); s_color, e_color = hex_to_rgba(default_gradient['start_color']), hex_to_rgba(default_gradient['end_color'])
                        direction = default_gradient.get('direction', 'horizontal')
                        if scope == 'word':
                            draw_gradient_text(final_text_img, word, (x_cursor, y_cursor), font, s_color, e_color, direction)
                        elif scope == 'letter':
                            char_x = x_cursor
                            for char in word:
                                draw_gradient_text(final_text_img, char, (char_x, y_cursor), font, s_color, e_color, direction)
                                char_x += font.getlength(char)
                        elif scope == 'block':
                            word_len = font.getlength(word); word_center_x = x_cursor + word_len / 2
                            ratio = word_center_x / canvas_width if canvas_width > 0 else 0
                            color = interpolate_color_rgba(s_color, e_color, ratio)
                            draw_final.text((x_cursor, y_cursor), word, font=font, fill=color)
                    else:
                        # USA A COR/GRADIENTE DA LINHA (nova funcionalidade ou cor padrão)
                        if line_gradient:
                            # Aplica gradiente da linha
                            s_color = hex_to_rgba(line_gradient['start_color'])
                            e_color = hex_to_rgba(line_gradient['end_color'])
                            direction = line_gradient.get('direction', 'horizontal')
                            draw_gradient_text(final_text_img, word, (x_cursor, y_cursor), font, s_color, e_color, direction)
                        else:
                            # Aplica cor sólida da linha
                            draw_final.text((x_cursor, y_cursor), word, font=font, fill=line_color)
                    
                    x_cursor += font.getlength(word + ' ')
                
                # Avança o cursor vertical usando a altura PADRÃO da linha.
                y_cursor += standard_line_height + line_spacing
            
            initial_alignment = layer.get('initial_alignment')
            if initial_alignment:
                if initial_alignment == 'center': canvas_x, canvas_y = (frame_width-canvas_width)//2, (frame_height-canvas_height)//2
                elif initial_alignment == 'top_left': canvas_x, canvas_y = 0, 0
                elif initial_alignment == 'top_right': canvas_x, canvas_y = frame_width - canvas_width, 0
                elif initial_alignment == 'bottom_left': canvas_x, canvas_y = 0, frame_height - canvas_height
                elif initial_alignment == 'bottom_right': canvas_x, canvas_y = frame_width - canvas_width, frame_height - canvas_height
            else:
                canvas_x = int(frame_width * layer.get('position_x_percent', 0))
                canvas_y = int(frame_height * layer.get('position_y_percent', 0))

            margin = layer.get('margin', {}); canvas_x += margin.get('left', 0) - margin.get('right', 0); canvas_y += margin.get('top', 0) - margin.get('bottom', 0)
            
            effects_layer = Image.new('RGBA', final_text_img.size, (0,0,0,0))
            draw_effects = ImageDraw.Draw(effects_layer)
            for item in highlighter_rects: draw_effects.rectangle(item["rect"], fill=item["color"])
            text_alpha_mask = final_text_img.getchannel('A')
            if 'shadow' in layer:
                s_cfg = layer['shadow']; shadow_img = Image.new('RGBA', final_text_img.size, (0,0,0,0)); shadow_img.paste(hex_to_rgba(s_cfg['color']), (0, 0), text_alpha_mask)
                if s_cfg.get('blur', 0) > 0: shadow_img = shadow_img.filter(ImageFilter.GaussianBlur(s_cfg['blur']))
                if s_cfg.get('opacity', 1.0) < 1.0:
                    alpha = shadow_img.getchannel('A'); alpha = ImageEnhance.Brightness(alpha).enhance(s_cfg['opacity']); shadow_img.putalpha(alpha)
                effects_layer.paste(shadow_img, (s_cfg['offset_x'], s_cfg['offset_y']), shadow_img)
            if 'outline' in layer:
                o_cfg = layer['outline']; o_width, o_color = o_cfg['width'], hex_to_rgba(o_cfg['color'])
                for x_off in range(-o_width, o_width + 1):
                    for y_off in range(-o_width, o_width + 1):
                        if x_off != 0 or y_off != 0: draw_effects.bitmap((x_off, y_off), text_alpha_mask, fill=o_color)
            final_canvas_with_effects = Image.alpha_composite(effects_layer, final_text_img)
            if 'opacity' in layer:
                alpha = final_canvas_with_effects.getchannel('A'); alpha = ImageEnhance.Brightness(alpha).enhance(layer['opacity']); final_canvas_with_effects.putalpha(alpha)
            if 'border' in layer:
                b_cfg = layer['border']; ImageDraw.Draw(final_canvas_with_effects).rectangle([(0, 0), (canvas_width - 1, canvas_height - 1)], outline=hex_to_rgba(b_cfg.get('color', '#FFF')), width=b_cfg.get('width', 2))
            thumbnail.alpha_composite(final_canvas_with_effects, (canvas_x, canvas_y))
    return thumbnail

# # --- EXEMPLO DE USO ---
# if __name__ == "__main__":
#     print("Gerando thumbnail final com todas as funcionalidades...")
#     fonts_dir, personagem_dir = "fonts", "personagem"
#     font_file, image_file = os.path.join(fonts_dir, "LeagueSpartan-Black.ttf"), os.path.join(personagem_dir, "saida_8.png")
#     if not os.path.exists(fonts_dir): os.makedirs(fonts_dir)
#     if not os.path.exists(personagem_dir): os.makedirs(personagem_dir)
#     if not os.path.exists(font_file): print(f"!!! FONTE NÃO ENCONTRADA EM '{font_file}' !!!")
#     if not os.path.exists(image_file):
#         print(f"Criando imagem de placeholder para '{image_file}'")
#         Image.new('RGBA', (600, 800), color=(255, 0, 255, 128)).save(image_file)
    
#     config_final = {
#         'frame': { 'width': 1920, 'height': 1080, 'background_color': '#000000'},
#         'layers': [
#             {
#                 'type': 'solid_color',
#                 'width_percent': 1.0, 'height_percent': 1.0,
#                 'initial_alignment': 'top_left',
#                 'gradient': {
#                     'start_color': '#87CEEB', 'end_color': '#000000',
#                     'direction': 'left_to_right',
#                     'start_percent': 0.6, 'end_percent': 0.75
#                 }
#             },
#             {
#                 'type': 'solid_color',
#                 'width_percent': 1.0, 'height_percent': 0.15,
#                 'initial_alignment': 'bottom_left',
#                 'gradient': {
#                     'start_color': '#FF0000', 'end_color': '#FF0000',
#                     'direction': 'left_to_right',
#                     'start_percent': 0.7, 'end_percent': 0.9
#                 }
#             },

#             { 
#                 'type': 'image', 'path': image_file, 
#                 'initial_alignment': 'bottom_right', 
#                 'resize_percent': 1.1, 
#                 'opacity': 1, # <-- Exemplo de uso da opacidade
#                 'brightness': 1,
#                 'margin': {'right': -100}
#             },
#             {
#                 'type': 'text',
#                 'text': 'PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE%  *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE%  *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE%',
#                 'font_path': font_file, 'font_size': 130,
#                 'initial_alignment': 'top_left',
#                 'width_percent': 0.75, 'height_percent': 0.85, 'padding': {'top': 20, 'bottom': 20, 'left': 30, 'right': 30},
#                 'auto_fit': True, 'min_font_size': 40, 'line_spacing': 10, 'horizontal_align': 'center', 'vertical_align': 'middle',
#                 'gradient': { 'scope': 'word', 'start_color': '#DCDCDC', 'end_color': '#DCDCDC'},
#                 'color_rules': {
#                     '*': { 'color': '#FFFF00' },
#                     '%': { 'gradient': {'scope': 'word', 'start_color': '#00F260', 'end_color': '#0575E6'}},
#                     '!': { 'highlighter': {'color': '#FF0000', 'padding_x': 15, 'padding_y': 15}}
#                 },
#                 'shadow': {'color': '#000000', 'offset_x': 8, 'offset_y': 8, 'blur': 3},
#                 'outline': {'color': '#000000', 'width': 1}, 
#                 'border': {'color': '#FFFFFF', 'width': 4}
#             }
#             ,
#             {
#                 'type': 'text',
#                 'text': 'frase 2  frase 2  frase 2  frase 2  frase 2  frase 2 ',
#                 'font_path': font_file, 'font_size': 70,
#                 'initial_alignment': 'bottom_left',
#                 'width_percent': 0.75, 'height_percent': 0.15, 'padding': {'top': 20, 'bottom': 20, 'left': 30, 'right': 30},
#                 'auto_fit': True, 'min_font_size': 40, 'line_spacing': 10, 'horizontal_align': 'left', 'vertical_align': 'middle',
#                 'gradient': { 'scope': 'word', 'start_color': '#7FFFD4', 'end_color': '#7FFFD4'},
#                 'color_rules': {
#                     '*': { 'color': '#FFFF00' },
#                     '%': { 'gradient': {'scope': 'word', 'start_color': '#00F260', 'end_color': '#0575E6'}},
#                     '!': { 'highlighter': {'color': '#000000CC', 'padding_x': 15, 'padding_y': 5}}
#                 },
#                 'shadow': {'color': '#000000', 'offset_x': 8, 'offset_y': 8, 'blur': 3},
#                 'outline': {'color': '#000000', 'width': 1}, 
#                 # 'border': {'color': '#FFFFFF', 'width': 4}
#             }
#         ]
#     }
    
#     try:
#         thumb = generate_youtube_thumbnail(config_final)
#         filename = "personagem/thumbnail_versao_final2.png"
#         thumb.save(filename)
#         print(f"\n\nThumbnail final gerada com sucesso! Salvo como '{filename}'")
#     except Exception as e:
#         print(f"\nOcorreu um erro ao gerar a thumbnail: {e}")
#         import traceback
#         traceback.print_exc()


def executar_geracao_thumbnail(config_thumbnail,pasta_saida,nome_arquivo_saida):
    thumb = generate_youtube_thumbnail(config_thumbnail)
    filename = os.path.join(pasta_saida, nome_arquivo_saida)
    thumb.save(filename)
    print(f"\nThumbnail final gerada com sucesso! Salvo como '{filename}'")
    return filename

