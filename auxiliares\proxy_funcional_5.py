import time
import os
import paramiko
import shlex
import json
import threading
import queue
import sys
from datetime import datetime
from google.oauth2 import service_account
from googleapiclient import discovery
from googleapiclient.errors import HttpError
from auxiliares.chaves_api_elevenlabs import atualizar_chave_api
from auxiliares.chaves_api_elevenlabs import obter_chaves_elevenlabs_api

# ==============================================================================
# 📌 CONFIGURAÇÕES GLOBAIS E ESTÁTICAS
# ==============================================================================
BASE_CONFIG = {
    # "PROJECT": 'automatizacaoyoutube-463617',
    "PROJECT": 'prime-script-464021-t8',
    "SERVICE_ACCOUNT_FILE": 'google_cloud/controler.json',
    "ZONES": ['us-central1-c', 'us-south1-a', 'us-west1-a', 'us-west2-b'],  # Lista de zonas rotativas
    "SSH_PRIVATE_KEY_PATH": 'google_cloud/meu_proxy_google',
    "SSH_PUBLIC_KEY_PATH": 'google_cloud/meu_proxy_google.pub',
    "SSH_USER": "padilha",
    "VM_PREFIX": "parallel-worker-",
    "MACHINE_TYPE": "e2-micro",
    "DISK_IMAGE_FAMILY": "projects/debian-cloud/global/images/family/debian-11",
    "DISK_SIZE_GB": 10,
    "DISK_TYPE_NAME": "pd-standard"
}

def get_zone_for_vm(vm_index, zones):
    """
    Retorna a zona para uma VM baseada no índice, distribuindo entre as zonas disponíveis
    """
    return zones[vm_index % len(zones)]

# ==============================================================================
# ✨ FUNÇÃO DE ORQUESTRAÇÃO PRINCIPAL "run()" ✨
# ==============================================================================
def run( phrases, num_vms, voice_id, model_id, output_folder):

    api_keys = obter_chaves_elevenlabs_api()
    run_id = datetime.now().strftime('%Y%m%d-%H%M%S')
    config=BASE_CONFIG
    
    """
    Orquestra a criação, uso paralelo e exclusão de um pool de VMs para gerar áudios,
    com tratamento de erro de API robusto.
    """
    
    # --- Funções Aninhadas ---
    print_lock = threading.Lock()
    def safe_print(message):
        with print_lock:
            print(message)

    def write_failure_report(key, reason, details="N/A"):
        report_dir = os.path.join(output_folder, "relatorios_de_falha")
        os.makedirs(report_dir, exist_ok=True)
        key_masked_for_filename = key[:6]
        filename = os.path.join(report_dir, f"falha_chave_{key_masked_for_filename}_{run_id}.txt")
        safe_print(f"📄 Registrando falha para a chave {key_masked_for_filename}... em {filename}")
        with open(filename, "w", encoding="utf-8") as f:
            f.write(f"Relatório de Falha para Chave de API\n" + "="*40 + "\n")
            f.write(f"ID da Execução: {run_id}\n")
            f.write(f"Chave (parcial): {key[:6]}...{key[-4:]}\n")
            f.write(f"Motivo da Falha: {reason}\n")
            f.write(f"Detalhes: {details}\n")

    def wait_for_operation(compute, operation, zone):
        safe_print("⏳ Aguardando operação da API...")
        while True:
            try:
                result = compute.zoneOperations().get(project=config["PROJECT"], zone=zone, operation=operation['name']).execute()
                if result['status'] == 'DONE':
                    if 'error' in result: raise Exception(f"Erro na operação: {result['error']}")
                    safe_print("✅ Operação concluída!")
                    return
            except HttpError as e:
                if e.resp.status == 404: safe_print("Operação não encontrada, assumindo concluída."); return
                safe_print(f"Erro ao verificar operação: {e}")
            time.sleep(3)

    def create_vm_for_pool(compute, vm_name, public_key_string, zone):
        safe_print(f"⚙️  Criando VM '{vm_name}' na zona {zone}...")
        disk_type_url = f'zones/{zone}/diskTypes/{config["DISK_TYPE_NAME"]}'
        config_body = { 'name': vm_name, 'machineType': f'zones/{zone}/machineTypes/{config["MACHINE_TYPE"]}', 'disks': [{'boot': True, 'autoDelete': True, 'initializeParams': { 'sourceImage': config["DISK_IMAGE_FAMILY"], 'diskSizeGb': config["DISK_SIZE_GB"], 'diskType': disk_type_url }}], 'networkInterfaces': [{'network': 'global/networks/default', 'accessConfigs': [{'type': 'ONE_TO_ONE_NAT', 'name': 'External NAT'}]}], 'metadata': {'items': [{'key': 'ssh-keys', 'value': f'{config["SSH_USER"]}:{public_key_string}'}]}}
        operation = compute.instances().insert(project=config["PROJECT"], zone=zone, body=config_body).execute()
        wait_for_operation(compute, operation, zone)
        safe_print(f"✅ VM '{vm_name}' criada na zona {zone}.")

    def delete_vm(compute, vm_name, zone):
        safe_print(f"🛑 Excluindo VM '{vm_name}' da zona {zone}...")
        try:
            operation = compute.instances().delete(project=config["PROJECT"], zone=zone, instance=vm_name).execute()
            wait_for_operation(compute, operation, zone)
            safe_print(f"✅ VM '{vm_name}' excluída da zona {zone}.")
        except HttpError as e:
            if e.resp.status == 404: safe_print(f"⚠️  VM '{vm_name}' não encontrada para exclusão.")
            else: safe_print(f"❌ Erro ao excluir a VM '{vm_name}': {e}")

    def start_vm(compute, vm_name, zone):
        safe_print(f"⚡ Ligando VM '{vm_name}' na zona {zone}...")
        op = compute.instances().start(project=config["PROJECT"], zone=zone, instance=vm_name).execute()
        wait_for_operation(compute, op, zone)
        safe_print("Aguardando 20s para boot...")
        time.sleep(20)
        instance = compute.instances().get(project=config["PROJECT"], zone=zone, instance=vm_name).execute()
        ip = instance['networkInterfaces'][0]['accessConfigs'][0]['natIP']
        safe_print(f"VM '{vm_name}' ligada com IP {ip} na zona {zone}")
        return ip

    def stop_vm(compute, vm_name, zone):
        safe_print(f"🔌 Desligando VM '{vm_name}' da zona {zone}...")
        try:
            op = compute.instances().stop(project=config["PROJECT"], zone=zone, instance=vm_name).execute()
            wait_for_operation(compute, op, zone)
            safe_print(f"VM '{vm_name}' desligada da zona {zone}.")
        except HttpError as e:
            if e.resp.status == 404: safe_print(f"⚠️  VM '{vm_name}' não encontrada para desligar.")
            else: safe_print(f"❌ Erro ao desligar a VM '{vm_name}': {e}")

    def _parse_error_details(error_body):
        try:
            error_json = json.loads(error_body)
            if isinstance(error_json, list) and error_json:
                error_data = error_json[0]
            else:
                error_data = error_json
            
            if isinstance(error_data, dict):
                return error_data.get("detail", {}).get("message", str(error_data))
            else:
                return str(error_data)
        except json.JSONDecodeError:
            return error_body

    # def tts_on_remote(ssh_client, text, api_key, local_output_path, current_voice_id, current_model_id):
    #     remote_path = f"/tmp/audio_{int(time.time())}.mp3"
    #     remote_status_path = f"/tmp/status_{int(time.time())}.txt"
        
    #     text_sanitizado = text.replace('\n', ' ').replace('\r', ' ').strip()
    #     if not text_sanitizado:
    #         safe_print("⚠️ Texto vazio após sanitização. Ignorando chamada ao TTS.")
    #         return False, "Texto vazio após sanitização"

    #     payload_dict = {"text": text_sanitizado, "model_id": current_model_id}
    #     json_payload = json.dumps(payload_dict)
    #     safe_json_data = shlex.quote(json_payload)

    #     url = f"https://api.elevenlabs.io/v1/text-to-speech/{current_voice_id}?output_format=mp3_44100_128"
        
    #     command = (
    #         f"curl -s -X POST '{url}' "
    #         f"-H 'Content-Type: application/json' "
    #         f"-H 'xi-api-key: {api_key}' "
    #         f"-d {safe_json_data} "
    #         f"-o '{remote_path}' "
    #         f"-w '%{{http_code}}' > '{remote_status_path}'"
    #     )
        
    #     safe_print(f"Enviando TTS com payload: {json_payload}")
    #     stdin, stdout, stderr = ssh_client.exec_command(command)
    #     exit_status = stdout.channel.recv_exit_status()
    #     sftp = ssh_client.open_sftp()

    #     with sftp.file(remote_status_path, 'r') as f:
    #         http_code = f.read().decode().strip()

    #     if http_code == '200':
    #         sftp.get(remote_path, local_output_path)
    #         sftp.close()
    #         ssh_client.exec_command(f"rm {remote_path} {remote_status_path}")
    #         return True, None
    #     else:
    #         error_fetch_command = (
    #             f"curl -s -X POST '{url}' "
    #             f"-H 'Content-Type: application/json' "
    #             f"-H 'xi-api-key: {api_key}' "
    #             f"-d {safe_json_data}"
    #         )
    #         stdin_err, stdout_err, stderr_err = ssh_client.exec_command(error_fetch_command)
    #         error_body = stdout_err.read().decode().strip()
    #         sftp.close()
    #         ssh_client.exec_command(f"rm {remote_path} {remote_status_path}")
    #         detailed_message = _parse_error_details(error_body)
    #         safe_print(f"Erro ao gerar áudio (HTTP {http_code}): {detailed_message}")
    #         return False, detailed_message

    def tts_on_remote(ssh_client, text, api_key, local_output_path, current_voice_id, current_model_id):
        # import time, json, shlex

        remote_path = f"/tmp/audio_{int(time.time())}.mp3"
        remote_status_path = f"/tmp/status_{int(time.time())}.txt"

        text_sanitizado = text.replace('\n', ' ').replace('\r', ' ').strip()
        if not text_sanitizado:
            safe_print("⚠️ Texto vazio após sanitização. Ignorando chamada ao TTS.")
            return False, "Texto vazio após sanitização"

        payload_dict = {
            "text": text_sanitizado,
            "model_id": current_model_id,
            "voice_settings": {
                "stability": 0.9,
                "similarity_boost": 0.95,
                "style": 0.0,
                # "speed": 0.95,
                 "use_speaker_boost": True
            },
            "output_format": "mp3_44100_192",
            "apply_text_normalization": "auto"
        }

        json_payload = json.dumps(payload_dict)
        safe_json_data = shlex.quote(json_payload)

        url = f"https://api.elevenlabs.io/v1/text-to-speech/{current_voice_id}"

        command = (
            f"curl -s -X POST '{url}' "
            f"-H 'Content-Type: application/json' "
            f"-H 'xi-api-key: {api_key}' "
            f"-d {safe_json_data} "
            f"-o '{remote_path}' "
            f"-w '%{{http_code}}' > '{remote_status_path}'"
        )

        safe_print(f"Enviando TTS com payload: {json_payload}")
        stdin, stdout, stderr = ssh_client.exec_command(command)
        exit_status = stdout.channel.recv_exit_status()
        sftp = ssh_client.open_sftp()

        with sftp.file(remote_status_path, 'r') as f:
            http_code = f.read().decode().strip()

        if http_code == '200':
            sftp.get(remote_path, local_output_path)
            sftp.close()
            ssh_client.exec_command(f"rm {remote_path} {remote_status_path}")
            return True, None
        else:
            error_fetch_command = (
                f"curl -s -X POST '{url}' "
                f"-H 'Content-Type: application/json' "
                f"-H 'xi-api-key: {api_key}' "
                f"-d {safe_json_data}"
            )
            stdin_err, stdout_err, stderr_err = ssh_client.exec_command(error_fetch_command)
            error_body = stdout_err.read().decode().strip()
            sftp.close()
            ssh_client.exec_command(f"rm {remote_path} {remote_status_path}")
            detailed_message = _parse_error_details(error_body)
            safe_print(f"Erro ao gerar áudio (HTTP {http_code}): {detailed_message}")
            return False, detailed_message

    def check_credits_on_remote(ssh_client, api_key):
        safe_print(f"Verificando créditos para a chave {api_key[:6]}...")
        command = f"curl -s -X GET 'https://api.elevenlabs.io/v1/user' -H 'xi-api-key: {api_key}' --fail"
        stdin, stdout, stderr = ssh_client.exec_command(command)
        exit_status = stdout.channel.recv_exit_status()
        if exit_status == 0:
            try:
                response_data = json.loads(stdout.read().decode())
                sub_data = response_data
                if isinstance(response_data, list) and response_data:
                    sub_data = response_data[0]
                if isinstance(sub_data, dict):
                    subscription = sub_data.get("subscription", {})
                    used = subscription.get("character_count", 0)
                    limit = subscription.get("character_limit", 0)
                    available = limit - used
                    safe_print(f"Saldo disponível: {available} caracteres.")
                    return available, None
                else:
                    return 0, "Resposta de créditos em formato inesperado (não é um objeto)."
            except json.JSONDecodeError as e: 
                return 0, f"Erro ao decodificar JSON de créditos: {e}"
        else: 
            error_details = stderr.read().decode().strip()
            safe_print(f"Erro ao verificar créditos: {error_details}"); return 0, error_details

    # --- O "Trabalhador" (Classe Aninhada com as modificações) ---
    class Worker(threading.Thread):
        def __init__(self, worker_name, phrases_q, keys_q, pub_key, cleanup_list, worker_index):
            super().__init__()
            self.name = worker_name
            self.phrases_queue = phrases_q
            self.keys_queue = keys_q
            self.public_key = pub_key
            self.cleanup_list = cleanup_list
            self.worker_index = worker_index
            self.zone = get_zone_for_vm(worker_index, config["ZONES"])
            self.vm_name = f"{config['VM_PREFIX']}{run_id}-{self.name.split('-')[-1]}"
            self.compute = None

        def run(self):
            try:
                credentials = service_account.Credentials.from_service_account_file(config["SERVICE_ACCOUNT_FILE"])
                self.compute = discovery.build('compute', 'v1', credentials=credentials)
                create_vm_for_pool(self.compute, self.vm_name, self.public_key, self.zone)
                self.cleanup_list.append((self.vm_name, self.zone))  # Salvar VM e zona para limpeza
                while not self.phrases_queue.empty():
                    try:
                        api_key = self.keys_queue.get_nowait()
                        safe_print(f"[{self.name}] pegou a chave '{api_key[:6]}...'.")
                        self.process_with_session(api_key)
                    except queue.Empty:
                        safe_print(f"[{self.name}] Não há mais chaves. Encerrando."); break
            except Exception as e:
                safe_print(f"💥💥💥 TRABALHADOR {self.name} FALHOU: {e} 💥💥💥")
            finally:
                safe_print(f"👷 Trabalhador {self.name} terminou seu ciclo de vida.")

        def process_with_session(self, api_key):
            ssh_connection = None
            try:
                ip = start_vm(self.compute, self.vm_name, self.zone)
                ssh_connection = paramiko.SSHClient(); ssh_connection.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                ssh_connection.connect(hostname=ip, username=config["SSH_USER"], key_filename=config["SSH_PRIVATE_KEY_PATH"], timeout=20)
                
                creditos, erro_credito = check_credits_on_remote(ssh_connection, api_key)
                if creditos <= 0:
                    write_failure_report(api_key, "Sem saldo inicial", erro_credito)
                    safe_print(f"🔑 Chave {api_key[:6]}... sem saldo. Atualizando status via API.") # <<<< ADICIONADO >>>>
                    atualizar_chave_api(api_key)                                                 # <<<< ADICIONADO >>>>
                    return

                while True:
                    try:
                        idx, frase = self.phrases_queue.get_nowait()
                    except queue.Empty: break

                    if creditos < len(frase):
                        write_failure_report(api_key, "Saldo esgotou", f"Acabou ao tentar processar a frase #{idx}")
                        safe_print(f"🔑 Chave {api_key[:6]}... esgotou. Atualizando status via API.") # <<<< ADICIONADO >>>>
                        atualizar_chave_api(api_key)                                                  # <<<< ADICIONADO >>>>
                        self.phrases_queue.put((idx, frase)); break
                    
                    safe_print(f"[{self.name}] processando frase #{idx}...")
                    output_path = os.path.join(output_folder, f"audio_{idx}.mp3")
                    
                    sucesso, erro_tts = tts_on_remote(ssh_connection, frase, api_key, output_path, voice_id, model_id)
                    if sucesso:
                        safe_print(f"[{self.name}] ✅ Frase #{idx} concluída!")
                        creditos -= len(frase); self.phrases_queue.task_done()
                    else:
                        write_failure_report(api_key, "Erro na API de TTS", erro_tts)
                        safe_print(f"🔑 Chave {api_key[:6]}... falhou na API. Atualizando status.") # <<<< ADICIONADO >>>>
                        atualizar_chave_api(api_key)                                               # <<<< ADICIONADO >>>>
                        self.phrases_queue.put((idx, frase)); break
            except Exception as e:
                write_failure_report(api_key, "Erro de Conexão ou Sessão", str(e))
                safe_print(f"🔑 Chave {api_key[:6]}... encontrou erro de sessão. Atualizando status.") # <<<< ADICIONADO >>>>
                atualizar_chave_api(api_key)                                                         # <<<< ADICIONADO >>>>
            finally:
                if ssh_connection: ssh_connection.close()
                if self.compute: stop_vm(self.compute, self.vm_name, self.zone)
    
    # --- O Corpo da Função `run` ---
    vms_para_deletar_no_final = []
    try:
        os.makedirs(output_folder, exist_ok=True)
        with open(config["SSH_PUBLIC_KEY_PATH"], 'r') as f: public_key = f.read().strip()
        phrases_queue = queue.Queue(); [phrases_queue.put(item) for item in enumerate(phrases, 1)]
        keys_queue = queue.Queue(); [keys_queue.put(key) for key in api_keys]

        safe_print(f"🚀 Iniciando processamento paralelo com {num_vms} VMs distribuídas entre {len(config['ZONES'])} zonas... 🚀")
        workers = []
        for i in range(num_vms):
            zone = get_zone_for_vm(i, config["ZONES"])
            safe_print(f"Worker-{i+1} será criado na zona: {zone}")
            worker = Worker(f"Worker-{i+1}", phrases_queue, keys_queue, public_key, vms_para_deletar_no_final, i)
            workers.append(worker); worker.start()

        for worker in workers: worker.join()
        
        if not phrases_queue.empty():
            safe_print(f"\n⚠️ ALERTA: O trabalho terminou, mas {phrases_queue.qsize()} frases não foram processadas.")
        else:
            safe_print("\n" + "="*25 + " SUCESSO! TODAS AS FRASES FORAM PROCESSADAS. " + "="*25)

    except (Exception, KeyboardInterrupt) as e:
        safe_print(f"\n❌ OCORREU UM ERRO OU INTERRUPÇÃO: {e}")
    finally:
        # FASE 3: LIMPEZA FINAL
        safe_print("\n" + "="*20 + " FASE DE LIMPEZA FINAL E GARANTIDA " + "="*20)
        report_dir = os.path.join(output_folder, "relatorios_de_falha")
        safe_print(f"Verifique a pasta '{report_dir}' por relatórios de chaves descartadas.")
        
        try:
            credentials = service_account.Credentials.from_service_account_file(config["SERVICE_ACCOUNT_FILE"])
            compute = discovery.build('compute', 'v1', credentials=credentials)
        except Exception as e_final:
            safe_print(f"Não foi possível criar serviço para limpeza: {e_final}. Delete as VMs manualmente.")
            return

        if vms_para_deletar_no_final:
            safe_print(f"Iniciando exclusão de todas as {len(vms_para_deletar_no_final)} VMs do pool...")
            for vm_name, zone in vms_para_deletar_no_final:
                delete_vm(compute, vm_name, zone)
            safe_print("Limpeza final concluída.")
        else:
            safe_print("Nenhuma VM para limpar.")

# ==============================================================================
# PONTO DE ENTRADA DO SCRIPT
# ==============================================================================
if __name__ == "__main__":
    start_time = time.time()
    
    # --- Seus parâmetros aqui ---
    # PREENCHA COM SUAS CHAVES REAIS
    chaves_para_usar = ["SUA_CHAVE_API_1_AQUI", "SUA_CHAVE_API_2_AQUI", "CHAVE_COM_FALHA_3"] 
    frases_para_narrar = [
        "Olá, este é o primeiro teste de áudio.", 
        "Esta é a segunda frase a ser processada.",
        "Vamos ver se a terceira frase consegue ser gerada.",
        "E finalmente, a quarta e última frase para este lote."
    ]
    quantidade_de_vms = 2 # Pode ser maior ou menor que o número de chaves
    id_da_voz = "JBFqnCBsd6RMkjVDRZzb" # ID de voz da ElevenLabs
    id_do_modelo = "eleven_multilingual_v2"
    pasta_de_saida = "audios_gerados"
    id_unico_da_execucao = datetime.now().strftime('%Y%m%d-%H%M%S')
    
    try:
        run(
            api_keys=chaves_para_usar,
            phrases=frases_para_narrar,
            num_vms=quantidade_de_vms,
            voice_id=id_da_voz,
            model_id=id_do_modelo,
            run_id=id_unico_da_execucao,
            output_folder=pasta_de_saida,
            config=BASE_CONFIG
        )
    finally:
        end_time = time.time()
        duration_seconds = end_time - start_time
        minutes, seconds = divmod(duration_seconds, 60)
        hours, minutes = divmod(minutes, 60)
        
        print("\n" + "="*50)
        print(f"⏱️  TEMPO DE EXECUÇÃO TOTAL: {int(hours):02d}h {int(minutes):02d}m {int(seconds):02d}s")
        print("="*50)
        print("\nScript encerrado.")