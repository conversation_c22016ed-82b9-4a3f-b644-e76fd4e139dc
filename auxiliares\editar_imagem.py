from PIL import Image, ImageEnhance, ImageOps

def editar_imagem(
    caminho_entrada: str, 
    caminho_saida: str, 
    brilho: float = 1.0, 
    contraste: float = 1.0,
    cor: float = 1.0, 
    vivacidade: float = 1.0,
    nitidez: float = 1.0,
    temperatura: int = 0,
    sepia: bool = False
):
    """
    Aplica um conjunto completo de ajustes a uma imagem.

    Parâmetros de Ajuste (1.0 = sem alteração):
    - brilho (float): > 1.0 clareia, < 1.0 escurece.
    - contraste (float): > 1.0 aumenta o contraste, < 1.0 diminui.
    - cor (float): > 1.0 mais saturação, 0.0 preto e branco.
    - vivacidade (float): > 1.0 aumenta a cor em áreas menos saturadas.
    - nitidez (float): > 1.0 aumenta a nitidez, < 1.0 desfoca.

    Parâmetros Especiais:
    - temperatura (int): Ajusta a temperatura da cor.
                         Valores > 0 deixam a imagem mais "quente" (amarelada).
                         Valores < 0 deixam a imagem mais "fria" (azulada).
                         Use valores como -50 a 50 para um bom controle.
    - sepia (bool): Se True, aplica um filtro sépia clássico à imagem.
    """
    try:
        # Abre a imagem de entrada e garante que está no modo RGB
        img = Image.open(caminho_entrada).convert('RGB')
        
        # --- 1. Ajuste de Brilho ---
        if brilho != 1.0:
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(brilho)

        # --- 2. Ajuste de Contraste ---
        if contraste != 1.0:
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(contraste)

        # --- 3. Ajuste de Temperatura de Cor ---
        if temperatura != 0:
            # Normaliza o valor da temperatura para um intervalo mais controlável
            fator_temp = temperatura / 100.0
            r, g, b = img.split()
            
            # Para aquecer: aumenta o vermelho, diminui o azul
            if fator_temp > 0:
                r = r.point(lambda i: i + (255 - i) * fator_temp)
                b = b.point(lambda i: i * (1 - fator_temp))
            # Para esfriar: aumenta o azul, diminui o vermelho
            else:
                b = b.point(lambda i: i + (255 - i) * abs(fator_temp))
                r = r.point(lambda i: i * (1 - abs(fator_temp)))
            
            img = Image.merge('RGB', (r, g, b))

        # --- 4. Ajuste de Vivacidade (Vibrance) ---
        if vivacidade != 1.0:
            hsv_img = img.convert('HSV')
            h, s, v = hsv_img.split()
            s = s.point(lambda i: int(i * (1 + (vivacidade - 1) * ((255 - i) / 255.0))))
            hsv_img = Image.merge('HSV', (h, s, v))
            img = hsv_img.convert('RGB')

        # --- 5. Ajuste de Cor (Saturação) ---
        if cor != 1.0:
            enhancer = ImageEnhance.Color(img)
            img = enhancer.enhance(cor)
            
        # --- 6. Ajuste de Nitidez ---
        if nitidez != 1.0:
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(nitidez)

        # --- 7. Filtro Sépia (aplicado por último) ---
        if sepia:
            # Converte para tons de cinza primeiro e depois colore com tom sépia
            img = ImageOps.grayscale(img)
            img = ImageOps.colorize(img, black="#4B3621", white="#DDC5A2")

        # Salva a imagem processada
        img.save(caminho_saida)
        print(f"Imagem editada com sucesso e salva em: {caminho_saida}")

    except FileNotFoundError:
        print(f"Erro: O arquivo de entrada não foi encontrado em '{caminho_entrada}'")
    except Exception as e:
        print(f"Ocorreu um erro durante o processamento da imagem: {e}")