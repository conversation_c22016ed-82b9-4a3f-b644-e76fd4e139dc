import requests

def obter_chaves_elevenlabs_api(base_url='http://144.22.232.201:5000'):
    try:
        url = f'{base_url}/api/obter_chaves_eleven_labs'
        response = requests.get(url)

        if response.status_code == 200:
            dados = response.json()

            # Se for lista, extrai as chaves
            if isinstance(dados, list):
                return [item.get('chave') for item in dados if 'chave' in item]
            
            # Se não for lista (ex: dict com 'error'), devolve lista vazia
            print(f"Resposta inesperada: {dados}")
            return []

        else:
            # Erro HTTP: devolve lista vazia
            print(f"Erro na API: {response.status_code}")
            try:
                print("Resposta:", response.json())
            except Exception:
                print("Resposta não é JSON válido.")
            return []

    except requests.RequestException as e:
        print(f"Erro na requisição: {e}")
        return []

# chaves = obter_chaves_elevenlabs_api()
# print(len(chaves))
# exit()



def atualizar_chave_api(chave, base_url='http://144.22.232.201:5000'):
    url = f"{base_url}/api/atualizar_chave/{chave}"
    
    try:
        response = requests.put(url)  # Sem corpo, pois a atualização é fixa no servidor
        if response.status_code == 200:
            print(response.json())  # {'message': 'Chave atualizada com sucesso'}
            return response.json()
        else:
            print(f"Erro na API: {response.status_code}")
            print(response.json())
            return None
    except requests.RequestException as e:
        print(f"Erro na requisição: {e}")
        return None
