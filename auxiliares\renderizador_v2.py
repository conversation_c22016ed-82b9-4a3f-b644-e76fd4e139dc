#novo codigo com melhorias

import os
import subprocess
from natsort import natsorted
import uuid
import tempfile
import shutil
import json
import math
from multiprocessing import Pool, cpu_count
import hashlib
import time

try:
    import stable_whisper
except ImportError:
    print("ERRO: Biblioteca 'stable-ts' não encontrada.")
    print("Por favor, instale-a executando no seu terminal: pip install stable-ts")
    exit()

caminho_ffmpeg = os.path.join(os.getcwd(), "bin")
os.environ["PATH"] += os.pathsep + caminho_ffmpeg

# =================================================================== #
# ======================== FUNÇÕES AUXILIARES ======================= #
# =================================================================== #

# =================================================================== #
# ================= CONFIGURAÇÕES DO EFEITO PÊNDULO ================ #
# =================================================================== #
# Configurações padrão para o efeito pêndulo melhorado
PENDULUM_CONFIG_DEFAULT = {
    'frequencia': 0.12,                  # Frequência do movimento (valores maiores = mais rápido)
    'rotacao_max_graus': 1.2,            # Ângulo máximo de rotação em graus
    'amplitude_horizontal': 8,           # Amplitude do movimento horizontal
    'amplitude_vertical': 3,             # Amplitude do movimento vertical
    'fase_vertical': 1.57,               # Fase do movimento vertical (PI/2 para movimento elíptico)
    'zoom_base': 1.2,                    # Zoom base para dar espaço ao movimento
    'suavizacao': True,                  # Ativar suavização do movimento
    'tipo_pendulo': 'completo'           # 'simples', 'horizontal', 'vertical', 'completo'
}

def gerar_filtro_pendulo_avancado(w, h, duracao_clip, fps, config_pendulo=None):
    """
    Gera um filtro FFmpeg avançado para efeito pêndulo.
    Versão corrigida para usar a sintaxe 'out_w' e 'out_h' do FFmpeg.
    """
    if config_pendulo is None:
        config_pendulo = PENDULUM_CONFIG_DEFAULT.copy()

    config = PENDULUM_CONFIG_DEFAULT.copy()
    config.update(config_pendulo)

    freq = config['frequencia']
    rot_max_graus = config['rotacao_max_graus']
    amp_h = config['amplitude_horizontal']
    amp_v = config['amplitude_vertical']
    zoom_base = config['zoom_base']
    tipo = config['tipo_pendulo']

    filtros = []

    # Aplica o zoom inicial
    filtros.append(f"scale={int(w * zoom_base)}:{int(h * zoom_base)}:eval=frame")

    # Aplica a rotação, se configurada
    if rot_max_graus > 0 and tipo in ['simples', 'completo', 'horizontal', 'vertical']:
        filtros.append(f"rotate='{math.radians(rot_max_graus)}*sin(2*PI*t/{duracao_clip}*{freq}*10)':fillcolor=black:ow=iw:oh=ih")

    # Expressão de tempo para o movimento
    time_expr = f"2*PI*t/{duracao_clip}*{freq}*10"

    # --- CORREÇÃO FINAL APLICADA AQUI ---
    # Substituímos 'w' e 'h' por 'out_w' e 'out_h' dentro das expressões.
    # out_w e out_h se referem ao primeiro e segundo parâmetros do crop (w e h).
    
    if tipo == 'horizontal':
        movimento_x = f"(iw-out_w)/2 + {amp_h}*cos({time_expr})"
        movimento_y = f"(ih-out_h)/2"
        filtros.append(f"crop={w}:{h}:x='{movimento_x}':y='{movimento_y}'")
    elif tipo == 'vertical':
        movimento_x = f"(iw-out_w)/2"
        movimento_y = f"(ih-out_h)/2 + {amp_v}*cos({time_expr})"
        filtros.append(f"crop={w}:{h}:x='{movimento_x}':y='{movimento_y}'")
    elif tipo == 'simples':
        movimento_x = f"(iw-out_w)/2 + {amp_h}*cos({time_expr})"
        movimento_y = f"(ih-out_h)/2"
        filtros.append(f"crop={w}:{h}:x='{movimento_x}':y='{movimento_y}'")
    else:  # 'completo'
        movimento_x = f"(iw-out_w)/2 + {amp_h}*sin({time_expr})"
        movimento_y = f"(ih-out_h)/2 + {amp_v}*sin({time_expr}+PI/2)"
        filtros.append(f"crop={w}:{h}:x='{movimento_x}':y='{movimento_y}'")
        
    return filtros

def limpar_cor_hex(cor):
    cor = cor.strip()
    if cor.startswith('#'): return cor[1:]
    if cor.startswith('0x'): return cor[2:]
    return cor

def hex_para_ass_bgr(hex_cor, alpha_hex='00'):
    r = hex_cor[0:2]; g = hex_cor[2:4]; b = hex_cor[4:6]
    return f"&H{alpha_hex}{b}{g}{r}&"

def obter_duracao_midia(caminho_midia):
    if not os.path.exists(caminho_midia):
        raise FileNotFoundError(f"O arquivo de mídia não foi encontrado: {caminho_midia}")
    comando = ["ffprobe", "-v", "error", "-show_entries", "format=duration", "-of", "json", caminho_midia]
    try:
        resultado = subprocess.run(comando, capture_output=True, text=True, check=True)
        return float(json.loads(resultado.stdout)["format"]["duration"])
    except Exception as e:
        raise RuntimeError(f"Falha ao obter duração de '{caminho_midia}': {e}")

def é_imagem(arquivo): return arquivo.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif'))
def é_video(arquivo): return arquivo.lower().endswith(('.mp4', '.mov', '.avi', '.mkv', '.webm'))
def é_audio(arquivo):
    if not arquivo or not isinstance(arquivo, str): return False
    return arquivo.lower().endswith(('.mp3', '.wav', '.aac', '.m4a', '.flac', '.ogg'))

# =================================================================== #
# ===================== FUNÇÃO DE LEGENDAS ========================== #
# =================================================================== #
def gerar_legendas_karaoke(caminho_audio, config_legenda, output_folder):
    print("🎤 Forçando a geração de novas legendas a cada execução... (Isso pode demorar)")
    if not os.path.exists(caminho_audio):
        print(f"⚠️ AVISO: Arquivo de áudio para legendas não encontrado em '{caminho_audio}'. Pulando geração de legendas.")
        return None
    caminho_saida_ass = os.path.join(output_folder, "legendas_geradas.ass")

    # Usar modelo e idioma configurados
    modelo = config_legenda.get('modelo_whisper', 'base')
    idioma = config_legenda.get('idioma', 'pt')

    print(f"🤖 Carregando modelo Whisper: {modelo}")
    print(f"🌍 Idioma selecionado: {idioma}")
    print("🔥 Configurando Whisper para usar GPU...")

    # Configurar para usar GPU se disponível
    import torch
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"🎯 Dispositivo selecionado para Whisper: {device}")

    # Carregar modelo com configurações otimizadas para GPU
    try:
        # Tentar com compute_type (versões mais recentes)
        model = stable_whisper.load_model(
            modelo,
            device=device,
            compute_type="float16" if device == "cuda" else "float32"
        )
    except TypeError:
        # Fallback para versões mais antigas sem compute_type
        print("⚠️  compute_type não suportado, usando configuração padrão...")
        model = stable_whisper.load_model(modelo, device=device)

    # Transcrever com configurações otimizadas
    print("🎤 Iniciando transcrição com aceleração GPU...")
    resultado = model.transcribe(
        caminho_audio,
        language=idioma if idioma != 'auto' else None,
        regroup=False,
        # Configurações para melhor performance em GPU
        beam_size=1,  # Reduz uso de memória
        best_of=1,    # Reduz uso de memória
        temperature=0.0,  # Determinístico
        compression_ratio_threshold=2.4,
        logprob_threshold=-1.0,
        no_speech_threshold=0.6,
        condition_on_previous_text=False  # Melhor para processamento paralelo
    )
    
    # Usar a função de divisão do stable-whisper é uma boa prática
    resultado = resultado.split_by_length(max_words=config_legenda.get('max_palavras_por_linha', 10))
    
    cor_primaria_hex = limpar_cor_hex(config_legenda['cor_fonte']); cor_destaque_hex = limpar_cor_hex(config_legenda['cor_destaque'])
    cor_contorno_hex = limpar_cor_hex(config_legenda.get('cor_contorno_degrade', '000000')); tamanho_destaque = config_legenda.get('tamanho_destaque', 105)
    
    with open(caminho_saida_ass, "w", encoding='utf-8') as f:
        f.write("[Script Info]\nTitle: Legendas Geradas\nScriptType: v4.00+\nWrapStyle: 0\n"); f.write(f"PlayResX: {config_legenda['largura_video']}\nPlayResY: {config_legenda['altura_video']}\n\n")
        f.write("[V4+ Styles]\n"); f.write("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n")
        f.write(f"Style: Default,{config_legenda['fonte']},{config_legenda['tamanho_fonte']},{hex_para_ass_bgr(cor_primaria_hex)},&HFFFFFF,{hex_para_ass_bgr(cor_contorno_hex)},&H80000000,-1,0,0,0,100,100,0,0,1,3,2,2,10,10,{config_legenda['posicao_vertical']},1\n\n")
        f.write("[Events]\n"); f.write("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n")
        
        for segmento in resultado.segments:
            for idx_palavra_atual, palavra_info in enumerate(segmento.words):
                start_time = palavra_info.start
                
                # <<< MUDANÇA PRINCIPAL PARA EVITAR PISCAR >>>
                # O tempo final de uma palavra é o tempo de início da PRÓXIMA palavra.
                # Isso faz com que a legenda permaneça na tela durante as pausas.
                if idx_palavra_atual < len(segmento.words) - 1:
                    # Se não for a última palavra, termina quando a próxima começar
                    end_time = segmento.words[idx_palavra_atual + 1].start
                else:
                    # Se for a última palavra, usa o tempo final do segmento inteiro
                    end_time = segmento.end

                # Evita eventos com duração zero ou negativa, que podem causar erros
                if end_time <= start_time:
                    continue

                start_time_str = f"{int(start_time // 3600):01}:{int((start_time % 3600) // 60):02}:{start_time % 60:05.2f}"
                end_time_str = f"{int(end_time // 3600):01}:{int((end_time % 3600) // 60):02}:{end_time % 60:05.2f}"
                
                linha_texto_final = ""
                for i, p in enumerate(segmento.words):
                    texto_da_palavra = p.word.strip()
                    if i == idx_palavra_atual:
                        tags = f"\\c{hex_para_ass_bgr(cor_destaque_hex)}\\fscx{tamanho_destaque}\\fscy{tamanho_destaque}"
                        linha_texto_final += f"{{{tags}}}{texto_da_palavra}{{\\r}} "
                    else:
                        linha_texto_final += f"{texto_da_palavra} "
                        
                f.write(f"Dialogue: 0,{start_time_str},{end_time_str},Default,,0,0,0,,{linha_texto_final.strip()}\n")

    print(f"✔️ Novas legendas .ass geradas com sucesso em: {caminho_saida_ass}")
    return caminho_saida_ass

# =================================================================== #
# ================== FUNÇÃO PARA PROCESSAR UMA CAMADA =============== #
# =================================================================== #
def processar_camada_individual(args):
    camada_config, output_folder, tamanho, fps, duracao_final, preset_render = args
    nome_camada = camada_config['nome']
    print(f"🚀 Iniciando processamento para a camada: '{nome_camada}'")
    pasta = camada_config.get('pasta'); duracao_imagem = camada_config.get('duracao_imagem', 5)
    remover_fundo = camada_config.get('remover_fundo', False); is_transparent = remover_fundo
    extensao = '.mov' if is_transparent else '.mp4'
    
    caminho_saida_video = os.path.join(output_folder, f"temp_{nome_camada.replace(' ', '_')}_video{extensao}")

    arquivos = natsorted([os.path.join(pasta, f) for f in os.listdir(pasta) if é_imagem(f) or é_video(f)])
    if not arquivos:
        print(f"⚠️ AVISO: Nenhum arquivo de mídia na pasta {pasta} para '{nome_camada}'. Pulando.")
        return None

    w, h = map(int, tamanho.split('x'))
    temp_dir = tempfile.mkdtemp(prefix=f"camada_{nome_camada.replace(' ', '_')}_")

    clipes_processados, duracoes_clips = [], []
    for idx, arquivo in enumerate(arquivos):
        nome_temp = os.path.join(temp_dir, f"clip_{idx}{extensao}")
        duracao_clip = obter_duracao_midia(arquivo) if é_video(arquivo) else duracao_imagem
        duracoes_clips.append(duracao_clip)
        params_entrada, params_aceleracao, filtros_vf = [], [], []
        if é_imagem(arquivo):
            params_entrada.extend(['-loop', '1', '-i', arquivo])
        else:
            # Configurações de aceleração GPU compatíveis
            params_aceleracao.extend([
                '-hwaccel', 'cuda'  # Aceleração básica mais compatível
            ])
            params_entrada.extend(['-i', arquivo])
        if é_imagem(arquivo):
            if camada_config.get('ativar_movimento'):
                frames_totais = int(duracao_clip * fps); tipo_mov = camada_config.get('tipo_movimento', 'zoom_in')
                fator_zoom = 0.0001 + (camada_config.get('velocidade_movimento', 0.5) / 10) * (0.001 - 0.0001)
                if tipo_mov == 'zoom_in':
                    filtros_vf.append(f"zoompan=z='min(max(zoom,pzoom)+{fator_zoom},1.5)':d={frames_totais}:x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':s={w}x{h}")
                elif tipo_mov == 'zoom_out':
                    filtros_vf.append(f"zoompan=z='if(lte(pzoom,1.0),1.5,max(1.001,pzoom-{fator_zoom}))':d={frames_totais}:x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':s={w}x{h}")
                elif tipo_mov == 'pendulo':
                    # Usa a nova função de pêndulo avançado
                    config_pendulo_camada = camada_config.get('config_pendulo', {})
                    # Ajusta a velocidade baseada na configuração da camada
                    if 'velocidade_movimento' in camada_config:
                        velocidade_fator = camada_config.get('velocidade_movimento', 5) / 10
                        config_pendulo_camada['frequencia'] = PENDULUM_CONFIG_DEFAULT['frequencia'] * (1 + velocidade_fator)

                    filtros_pendulo = gerar_filtro_pendulo_avancado(w, h, duracao_clip, fps, config_pendulo_camada)
                    filtros_vf.extend(filtros_pendulo)
        else:
            if camada_config.get('ativar_movimento'):
                velocidade_fator = 1 + (camada_config.get('velocidade_movimento', 1) / 10); tipo_mov = camada_config.get('tipo_movimento', 'zoom_in')
                if tipo_mov == 'zoom_in':
                    filtros_vf.append(f"scale=w='iw*min(1.5,(1+t/{duracao_clip}*0.5*{velocidade_fator}))':h=-1:eval=frame,crop={w}:{h}")
                elif tipo_mov == 'zoom_out':
                    filtros_vf.append(f"scale=w='iw*max(1,(1.5-t/{duracao_clip}*0.5*{velocidade_fator}))':h=-1:eval=frame,crop={w}:{h}")
                elif tipo_mov == 'pendulo':
                    # Usa a nova função de pêndulo avançado para vídeos também
                    config_pendulo_camada = camada_config.get('config_pendulo', {})
                    if 'velocidade_movimento' in camada_config:
                        velocidade_fator_pendulo = camada_config.get('velocidade_movimento', 5) / 10
                        config_pendulo_camada['frequencia'] = PENDULUM_CONFIG_DEFAULT['frequencia'] * (1 + velocidade_fator_pendulo)

                    filtros_pendulo = gerar_filtro_pendulo_avancado(w, h, duracao_clip, fps, config_pendulo_camada)
                    filtros_vf.extend(filtros_pendulo)
        if camada_config.get('ativar_brilho'): filtros_vf.append(f"eq=brightness={camada_config.get('valor_brilho', 0.0)}")
        filtros_vf.append(f"fps={fps}")
        if not camada_config.get('ativar_movimento'): filtros_vf.append(f"scale={w}:{h},setsar=1")
        else: filtros_vf.append("setsar=1")
        if is_transparent:
            filtros_vf.append(f"chromakey=color=0x{limpar_cor_hex(camada_config.get('cor_remover', '00FF00'))}:similarity={camada_config.get('similarity', 0.1)}:blend={camada_config.get('blend', 0.02)}")
            filtros_vf.append("format=rgba"); codec_saida = ['-c:v', 'qtrle']
        else:
            # Configurações compatíveis com aceleração GPU
            filtros_vf.append("format=yuv420p")  # Formato compatível
            codec_saida = [
                '-c:v', 'h264_nvenc',
                '-preset', preset_render,
                '-cq', '21',
                '-gpu', '0',  # Especificar GPU 0
                '-rc', 'vbr',  # Rate control variável
                '-spatial_aq', '1',  # Adaptive quantization espacial
                '-temporal_aq', '1'   # Adaptive quantization temporal
            ]
        comando_chunk = ['ffmpeg', '-y', *params_aceleracao, *params_entrada, '-t', str(duracao_clip), '-vf', ",".join(filtros_vf), *codec_saida, '-an', nome_temp]
        subprocess.run(comando_chunk, check=True, capture_output=True, text=True)
        clipes_processados.append(nome_temp)

    lista_concat_path = os.path.join(temp_dir, "lista_arquivos_base.txt")
    with open(lista_concat_path, 'w', encoding='utf-8') as f:
        for clip in clipes_processados: f.write(f"file '{os.path.abspath(clip)}'\n")
    caminho_video_concatenado = os.path.join(temp_dir, f"concatenated{extensao}")
    comando_concat_base = ['ffmpeg', '-y', '-f', 'concat', '-safe', '0', '-i', lista_concat_path, '-c', 'copy', '-an', caminho_video_concatenado]
    subprocess.run(comando_concat_base, check=True, capture_output=True, text=True)
    
    duracao_ciclo_visual = sum(duracoes_clips)
    duracao_pausa = camada_config.get('duracao_pausa_loop', 0) if camada_config.get('ativar_pausa_loop', False) else 0
    duracao_ciclo_total = duracao_ciclo_visual + duracao_pausa
    
    video_base_para_loop = caminho_video_concatenado
    if duracao_pausa > 0:
        caminho_pausa = os.path.join(temp_dir, f"pause{extensao}")
        cmd_pausa = ['ffmpeg', '-y', '-f', 'lavfi']
        if is_transparent: cmd_pausa.extend(['-i', f'color=c=black@0.0:s={w}x{h}:d={duracao_pausa}', '-c:v', 'qtrle', '-an', caminho_pausa])
        else: cmd_pausa.extend(['-i', f'color=c=black:s={w}x{h}:d={duracao_pausa}', '-c:v', 'h264_nvenc', '-preset', preset_render, '-pix_fmt', 'yuv420p', '-an', caminho_pausa])
        subprocess.run(cmd_pausa, check=True, capture_output=True, text=True)
        lista_com_pausa_path = os.path.join(temp_dir, "lista_com_pausa.txt")
        video_com_pausa_path = os.path.join(temp_dir, f"concatenated_with_pause{extensao}")
        with open(lista_com_pausa_path, 'w', encoding='utf-8') as f:
            f.write(f"file '{os.path.abspath(caminho_video_concatenado)}'\n"); f.write(f"file '{os.path.abspath(caminho_pausa)}'\n")
        cmd_concat_pausa = ['ffmpeg', '-y', '-f', 'concat', '-safe', '0', '-i', lista_com_pausa_path, '-c', 'copy', video_com_pausa_path]
        subprocess.run(cmd_concat_pausa, check=True, capture_output=True, text=True)
        video_base_para_loop = video_com_pausa_path

    if duracao_final and duracao_ciclo_total > 0 and duracao_ciclo_total < duracao_final:
        num_loops = math.ceil(duracao_final / duracao_ciclo_total)
        loop_list_path = os.path.join(temp_dir, "loop_list.txt")
        with open(loop_list_path, 'w', encoding='utf-8') as f:
            for _ in range(int(num_loops)): f.write(f"file '{os.path.abspath(video_base_para_loop)}'\n")
        
        # <<< MUDANÇA PRINCIPAL: CRIANDO O VÍDEO COM MARGEM DE SEGURANÇA >>>
        # Não cortamos mais na duração exata. Deixamos passar um pouco.
        # O corte final e preciso será feito na FASE 3.
        # Adicionamos +2 segundos para garantir que haja material de vídeo suficiente.
        duracao_com_margem = duracao_final + 2
        comando_loop = ['ffmpeg', '-y', '-f', 'concat', '-safe', '0', '-i', loop_list_path, '-c', 'copy', '-an', '-t', str(duracao_com_margem), caminho_saida_video]
        subprocess.run(comando_loop, check=True, capture_output=True, text=True)
    else:
        shutil.copy(video_base_para_loop, caminho_saida_video)
        
    lista_de_timestamps = []
    config_transicao_camada = camada_config.get('audio_transicao', {})
    if config_transicao_camada.get('ativar'):
        caminho_audio = config_transicao_camada.get('caminho')
        if é_audio(caminho_audio) and os.path.exists(caminho_audio):
            timestamps_base_ciclo = [sum(duracoes_clips[:i+1]) for i in range(len(duracoes_clips))]
            if duracao_final and duracao_ciclo_total > 0:
                num_ciclos = math.ceil(duracao_final / duracao_ciclo_total)
                for i in range(int(num_ciclos)):
                    offset = i * duracao_ciclo_total
                    for ts in timestamps_base_ciclo:
                        timestamp_final = offset + ts
                        if timestamp_final < duracao_final:
                            lista_de_timestamps.append(timestamp_final)
            else:
                lista_de_timestamps = timestamps_base_ciclo

    shutil.rmtree(temp_dir)
    print(f"✅ Concluído processamento para: '{nome_camada}'")
    
    return {
        'video_path': caminho_saida_video, 
        'transition_timestamps': sorted(list(set(lista_de_timestamps))),
        'transition_config': config_transicao_camada,
        'config': camada_config
    }
            
# =================================================================== #
# ========================= FUNÇÃO PRINCIPAL ======================== #
# =================================================================== #
def renderizar_video_completo(config_camadas, config_audio, config_legenda, config_ducking, output_folder, tamanho, fps, preset_render, limite_processos):
    if os.path.exists(output_folder):
        shutil.rmtree(output_folder)
    os.makedirs(output_folder, exist_ok=True)
    
    arquivos_temporarios_para_limpar = []
    caminho_arquivo_legendas, duracao_final = None, None

    print("\n" + "="*50 + "\n" + " FASE 1: PREPARAÇÃO DAS CAMADAS (VÍDEO E TIMESTAMPS) ".center(50) + "\n" + "="*50)
    narracao_conf = config_audio.get('narracao', {})
    if narracao_conf.get('ativar') and é_audio(narracao_conf.get('caminho')):
        narracao_path = narracao_conf['caminho']
        if os.path.exists(narracao_path):
            duracao_final = obter_duracao_midia(narracao_path)
            print(f"✔️ Narração definiu a duração do vídeo para: {duracao_final:.2f}s.")
            
            if config_legenda.get('ativar_legendas'):
                caminho_arquivo_legendas = gerar_legendas_karaoke(narracao_path, config_legenda, output_folder)
        else: 
            print(f"⚠️ AVISO: Arquivo de narração não encontrado em '{narracao_path}'. A duração do vídeo não será definida pelo áudio.")
            duracao_final = None

    camadas_ativas = [c for c in config_camadas if c.get('ativar_camada', False)]
    args_para_processar = [(camada, output_folder, tamanho, fps, duracao_final, preset_render) for camada in camadas_ativas]
    
    if limite_processos > 1 and len(args_para_processar) > 1:
        with Pool(processes=limite_processos) as pool:
            resultados = pool.map(processar_camada_individual, args_para_processar)
    else:
        resultados = [processar_camada_individual(arg) for arg in args_para_processar]

    videos_gerados = []
    dados_de_transicao = {}
    for res in filter(None, resultados):
        videos_gerados.append(res)
        arquivos_temporarios_para_limpar.append(res['video_path'])
        if res.get('transition_timestamps'):
            caminho_som = res['transition_config'].get('caminho')
            if caminho_som not in dados_de_transicao:
                dados_de_transicao[caminho_som] = {'timestamps': [], 'config': res['transition_config']}
            dados_de_transicao[caminho_som]['timestamps'].extend(res['transition_timestamps'])
            
    videos_gerados.sort(key=lambda x: [c['nome'] for c in config_camadas].index(x['config']['nome']))
    
    if not videos_gerados: raise Exception("Nenhuma camada de vídeo foi ativada.")
    print("\n✅ FASE 1 CONCLUÍDA.")

    print("\n" + "="*50 + "\n" + " FASE 2: EMPILHAMENTO DE VÍDEO (SEM ÁUDIO) ".center(50) + "\n" + "="*50)
    base_video_path = videos_gerados[0]['video_path']
    for i in range(1, len(videos_gerados)):
        camada_atual = videos_gerados[i]; overlay_video_path = camada_atual['video_path']
        alpha = camada_atual['config'].get('transparencia_alpha', 1.0)
        temp_output_path = os.path.join(output_folder, f"temp_base_{i}.mp4")
        print(f"   [ETAPA {i}/{len(videos_gerados)-1}] Sobrepondo vídeo '{camada_atual['config']['nome']}'...")
        
        # Filtro compatível com aceleração GPU (fallback para overlay padrão)
        filter_complex_str = f"[1:v]format=rgba,colorchannelmixer=aa={alpha}[ovl];[0:v][ovl]overlay=main_w/2-overlay_w/2:main_h/2-overlay_h/2,format=yuv420p"

        comando_incremental = [
            'ffmpeg', '-y',
            '-hwaccel', 'cuda',
            '-i', base_video_path,
            '-i', overlay_video_path,
            '-filter_complex', filter_complex_str,
            '-c:v', 'h264_nvenc',
            '-preset', preset_render,
            '-cq', '19',
            '-gpu', '0',
            '-rc', 'vbr',
            '-an',
            temp_output_path
        ]
        
        subprocess.run(comando_incremental, check=True, capture_output=True)
        if i > 1: os.remove(base_video_path)
        base_video_path = temp_output_path
        arquivos_temporarios_para_limpar.append(base_video_path)
    print("\n✅ FASE 2 CONCLUÍDA.")

    video_final_sem_audio = base_video_path

    print("\n" + "="*50 + "\n" + " FASE 3: FINALIZAÇÃO (ÁUDIO E LEGENDAS) ".center(50) + "\n" + "="*50)

    audios_de_transicao_finais = []
    temp_dir_audio = tempfile.mkdtemp(prefix="audio_final_")
    for caminho_som, dados in dados_de_transicao.items():
        todos_os_timestamps = sorted(list(set(dados['timestamps'])))
        if not todos_os_timestamps: continue

        print(f"   🎶 Gerando áudio de transição para '{os.path.basename(caminho_som)}'...")
        batch_size = 50
        timestamp_batches = [todos_os_timestamps[i:i + batch_size] for i in range(0, len(todos_os_timestamps), batch_size)]
        batch_audio_files = []
        print(f"       - Processando {len(todos_os_timestamps)} sons em {len(timestamp_batches)} lotes...")

        for i, batch in enumerate(timestamp_batches):
            batch_output_path = os.path.join(temp_dir_audio, f"batch_{os.path.basename(caminho_som)}_{i}.wav")
            num_swooshes_in_batch = len(batch)
            split_tags = "".join([f"[s{j}]" for j in range(num_swooshes_in_batch)])
            filtros_individuais = [f"[0:a]asplit={num_swooshes_in_batch}{split_tags}"]
            tags_para_mixar = []
            config_transicao = dados['config']
            fade_len = config_transicao.get('duracao_fade', 0.5); swoosh_duration = obter_duracao_midia(caminho_som)
            start_time_fade_out = max(0, swoosh_duration - fade_len)
            for j, ts in enumerate(batch):
                delay_ms = int(ts * 1000)
                in_tag = f"[s{j}]"; out_tag = f"[out{j}]"
                tags_para_mixar.append(out_tag)
                volume_filter = f"volume={config_transicao.get('volume', 1.0)}"
                fade_filter = f"afade=t=in:d={fade_len},afade=t=out:d={fade_len}:start_time={start_time_fade_out}"
                delay_filter = f"adelay={delay_ms}|{delay_ms}"
                filtros_individuais.append(f"{in_tag}{volume_filter},{fade_filter},{delay_filter}{out_tag}")
            filtro_amix = f"{''.join(tags_para_mixar)}amix=inputs={num_swooshes_in_batch}:normalize=0[aout]"
            filtro_complexo_final = ";".join(filtros_individuais) + ";" + filtro_amix
            comando_batch = ['ffmpeg', '-y', '-i', caminho_som, '-filter_complex', filtro_complexo_final, '-map', '[aout]', batch_output_path]
            subprocess.run(comando_batch, check=True, capture_output=True)
            batch_audio_files.append(batch_output_path)
        
        caminho_audio_final_camada = os.path.join(output_folder, f"transicoes_{os.path.basename(caminho_som)}.wav")
        if len(batch_audio_files) > 1:
            inputs_final_mix = [item for f in batch_audio_files for item in ['-i', f]]
            mix_tags = "".join([f'[{i}:a]' for i in range(len(batch_audio_files))])
            filtro_final_mix = f"{mix_tags}amix=inputs={len(batch_audio_files)}[aout]"
            comando_final_audio = ['ffmpeg', '-y', *inputs_final_mix, '-filter_complex', filtro_final_mix, '-map', '[aout]', '-t', str(duracao_final), caminho_audio_final_camada]
            subprocess.run(comando_final_audio, check=True, capture_output=True)
        elif len(batch_audio_files) == 1:
            shutil.move(batch_audio_files[0], caminho_audio_final_camada)
        
        audios_de_transicao_finais.append(caminho_audio_final_camada)
        arquivos_temporarios_para_limpar.append(caminho_audio_final_camada)
    shutil.rmtree(temp_dir_audio)

    caminho_saida_final = os.path.join(output_folder, "video_final.mp4")
    comando_final = ['ffmpeg', '-y', '-i', video_final_sem_audio]
    cadeia_filtros, trilhas_para_mixagem_final = [], []
    input_idx = 1
    
    audio_principais = {k: v for k, v in config_audio.items() if v.get('ativar')}
    for conf in audio_principais.values():
        if é_audio(conf.get('caminho')) and os.path.exists(conf.get('caminho')):
            comando_final.extend(['-i', conf.get('caminho')])
    for audio_path in audios_de_transicao_finais:
        comando_final.extend(['-i', audio_path])

    narracao_tag, trilha_ambiente_final = None, None
    trilhas_de_ambiente = []
    
    for nome, conf in audio_principais.items():
        if é_audio(conf.get('caminho')) and os.path.exists(conf.get('caminho')):
            stream_tag = f"[a_{nome}]"; input_stream = f"[{input_idx}:a]"
            if 'narracao' in nome:
                cadeia_filtros.append(f"{input_stream}volume={conf.get('volume', 1.0)}{stream_tag}"); narracao_tag = stream_tag
            elif 'fundo' in nome:
                cadeia_filtros.append(f"{input_stream}volume={conf.get('volume', 0.4)},aloop=loop=-1:size=2e9{stream_tag}"); trilhas_de_ambiente.append(stream_tag)
            input_idx += 1
            
    for i in range(len(audios_de_transicao_finais)):
        stream_tag = f"[a_trans_{i}]"; input_stream = f"[{input_idx}:a]"
        cadeia_filtros.append(f"{input_stream}volume=1.0{stream_tag}")
        trilhas_de_ambiente.append(stream_tag)
        input_idx += 1
        
    if trilhas_de_ambiente:
        ambiente_mixado_tag = "[audio_ambiente]"
        cadeia_filtros.append(f"{''.join(trilhas_de_ambiente)}amix=inputs={len(trilhas_de_ambiente)}:duration=first{ambiente_mixado_tag}")
        trilha_ambiente_final = ambiente_mixado_tag
        
    if narracao_tag and trilha_ambiente_final and config_ducking.get('ativar'):
        narracao_sidechain_tag = "[a_narr_side]"
        for i, filtro in enumerate(cadeia_filtros):
            if filtro.endswith(f"{narracao_tag}"):
                cadeia_filtros[i] = filtro.replace(narracao_tag, f",asplit=2{narracao_tag}{narracao_sidechain_tag}")
                break
        duck_conf = config_ducking; ambiente_ducked_tag = "[ambiente_ducked]"
        cadeia_filtros.append(f"{trilha_ambiente_final}{narracao_sidechain_tag}sidechaincompress=threshold={duck_conf['threshold']}:ratio={duck_conf['ratio']}:attack={duck_conf['attack']*1000}:release={duck_conf['release']*1000}:makeup=1{ambiente_ducked_tag}")
        trilha_ambiente_final = ambiente_ducked_tag
    
    if narracao_tag: trilhas_para_mixagem_final.append(narracao_tag)
    if trilha_ambiente_final: trilhas_para_mixagem_final.append(trilha_ambiente_final)
    
    if caminho_arquivo_legendas:
        # --- INÍCIO DA ALTERAÇÃO ---
        print("🔎 Verificando configuração de fonte para legendas...")
        caminho_abs_legenda = os.path.abspath(caminho_arquivo_legendas).replace('\\', '/').replace(':', '\\:')
        
        # Verifica se um caminho de fonte personalizado foi fornecido na configuração
        caminho_fonte_personalizado = config_legenda.get('caminho_fonte')
        if caminho_fonte_personalizado and os.path.exists(caminho_fonte_personalizado):
            # Se sim, usa o DIRETÓRIO do arquivo de fonte especificado
            caminho_pasta_fontes = os.path.dirname(os.path.abspath(caminho_fonte_personalizado))
            print(f"✔️ Usando diretório de fonte personalizado: '{caminho_pasta_fontes}'")
        else:
            # Caso contrário, usa o comportamento padrão: a pasta 'fonts' na raiz do projeto
            caminho_pasta_fontes = os.path.abspath('fonts')
            print(f"✔️ Usando pasta de fontes padrão: '{caminho_pasta_fontes}'")
        
        # Formata o caminho do diretório da fonte para o FFmpeg
        caminho_pasta_fontes_ffmpeg = caminho_pasta_fontes.replace('\\', '/').replace(':', '\\:')
        
        # Adiciona o filtro 'ass' ao início da cadeia de filtros com o 'fontsdir' correto
        cadeia_filtros.insert(0, f"[0:v]setpts=PTS-STARTPTS,ass=filename='{caminho_abs_legenda}':fontsdir='{caminho_pasta_fontes_ffmpeg}'[vout]")
        # --- FIM DA ALTERAÇÃO ---
    else:
        # Se não houver legendas, apenas ajusta os timestamps do vídeo
        cadeia_filtros.insert(0, "[0:v]setpts=PTS-STARTPTS[vout]")

    final_audio_map = "[aout]"
    if trilhas_para_mixagem_final:
        cadeia_filtros.append(f"{''.join(trilhas_para_mixagem_final)}amix=inputs={len(trilhas_para_mixagem_final)}:duration=first,alimiter=limit=0.95{final_audio_map}")
        comando_final.extend(['-filter_complex', ";".join(cadeia_filtros), '-map', '[vout]', '-map', final_audio_map])
    else:
        comando_final.extend(['-filter_complex', ";".join(cadeia_filtros), '-map', '[vout]'])
    
    if duracao_final: comando_final.extend(['-t', str(duracao_final)])
    
    # <<< CONFIGURAÇÕES GPU COMPATÍVEIS >>>
    # Configurações otimizadas e compatíveis para h264_nvenc
    comando_final.extend([
        '-c:v', 'h264_nvenc',
        '-preset', preset_render,
        '-cq', '23',
        '-pix_fmt', 'yuv420p',
        '-gpu', '0',  # Usar GPU 0
        '-rc', 'vbr',  # Rate control variável
        '-spatial_aq', '1',  # Adaptive quantization espacial
        '-temporal_aq', '1'  # Adaptive quantization temporal
    ])

    if trilhas_para_mixagem_final:
        comando_final.extend(['-c:a', 'aac', '-b:a', '192k'])
    
    comando_final.append(caminho_saida_final)
    print("\n" + "="*50 + "\n⚙️   Comando Final:\n" + " ".join(f'"{c}"' if " " in c else c for c in comando_final) + "\n" + "="*50 + "\n")
    subprocess.run(comando_final, check=True)
    
    print("🧹 Limpando todos os arquivos temporários...")
    for caminho in arquivos_temporarios_para_limpar:
        if os.path.exists(caminho): os.remove(caminho)
    print("\n✅ Processamento concluído com sucesso!")