#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Drive Uploader - Upload automático de arquivos ZIP para Google Drive
"""

import os
import json
from datetime import datetime
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from google.oauth2 import service_account
from googleapiclient.errors import HttpError


class GoogleDriveUploader:
    def __init__(self, credentials_path='google_drive/credentials.json'):
        """
        Inicializa o uploader do Google Drive
        
        Args:
            credentials_path: Caminho para o arquivo de credenciais JSON
        """
        self.credentials_path = credentials_path
        self.service = None
        self.pasta_principal_id = None
        
    def autenticar(self):
        """Autentica com o Google Drive usando service account"""
        try:
            print("🔐 Autenticando com Google Drive...")
            
            if not os.path.exists(self.credentials_path):
                print(f"❌ Arquivo de credenciais não encontrado: {self.credentials_path}")
                return False
            
            # Definir escopos necessários
            SCOPES = ['https://www.googleapis.com/auth/drive']
            
            # Carregar credenciais
            credentials = service_account.Credentials.from_service_account_file(
                self.credentials_path, scopes=SCOPES
            )
            
            # Construir serviço
            self.service = build('drive', 'v3', credentials=credentials)
            
            print("✅ Autenticação realizada com sucesso!")
            return True
            
        except Exception as e:
            print(f"❌ Erro na autenticação: {e}")
            return False
    
    def criar_pasta(self, nome_pasta, pasta_pai_id=None):
        """
        Cria uma pasta no Google Drive
        
        Args:
            nome_pasta: Nome da pasta a ser criada
            pasta_pai_id: ID da pasta pai (None para raiz)
            
        Returns:
            str: ID da pasta criada ou None se houver erro
        """
        try:
            print(f"📁 Criando pasta: {nome_pasta}")
            
            file_metadata = {
                'name': nome_pasta,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            if pasta_pai_id:
                file_metadata['parents'] = [pasta_pai_id]
            
            pasta = self.service.files().create(body=file_metadata, fields='id').execute()
            pasta_id = pasta.get('id')
            
            print(f"✅ Pasta criada com ID: {pasta_id}")
            return pasta_id
            
        except HttpError as e:
            print(f"❌ Erro ao criar pasta: {e}")
            return None
    
    def verificar_ou_criar_pasta(self, nome_pasta, pasta_pai_id=None):
        """
        Verifica se uma pasta existe, se não existir, cria
        
        Args:
            nome_pasta: Nome da pasta
            pasta_pai_id: ID da pasta pai
            
        Returns:
            str: ID da pasta
        """
        try:
            # Buscar pasta existente
            query = f"name='{nome_pasta}' and mimeType='application/vnd.google-apps.folder'"
            if pasta_pai_id:
                query += f" and '{pasta_pai_id}' in parents"
            
            results = self.service.files().list(q=query, fields="files(id, name)").execute()
            items = results.get('files', [])
            
            if items:
                print(f"📁 Pasta '{nome_pasta}' já existe")
                return items[0]['id']
            else:
                return self.criar_pasta(nome_pasta, pasta_pai_id)
                
        except HttpError as e:
            print(f"❌ Erro ao verificar pasta: {e}")
            return self.criar_pasta(nome_pasta, pasta_pai_id)
    
    def fazer_upload_arquivo(self, caminho_arquivo, pasta_destino_id=None, nome_personalizado=None):
        """
        Faz upload de um arquivo para o Google Drive
        
        Args:
            caminho_arquivo: Caminho local do arquivo
            pasta_destino_id: ID da pasta de destino
            nome_personalizado: Nome personalizado para o arquivo (opcional)
            
        Returns:
            str: ID do arquivo no Drive ou None se houver erro
        """
        try:
            if not os.path.exists(caminho_arquivo):
                print(f"❌ Arquivo não encontrado: {caminho_arquivo}")
                return None
            
            nome_arquivo = nome_personalizado or os.path.basename(caminho_arquivo)
            tamanho_mb = os.path.getsize(caminho_arquivo) / (1024 * 1024)
            
            print(f"📤 Fazendo upload: {nome_arquivo} ({tamanho_mb:.2f} MB)")
            
            file_metadata = {'name': nome_arquivo}
            if pasta_destino_id:
                file_metadata['parents'] = [pasta_destino_id]
            
            media = MediaFileUpload(caminho_arquivo, resumable=True)
            
            request = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            )
            
            response = None
            while response is None:
                status, response = request.next_chunk()
                if status:
                    print(f"📊 Upload: {int(status.progress() * 100)}%")
            
            arquivo_id = response.get('id')
            print(f"✅ Upload concluído! ID: {arquivo_id}")
            
            return arquivo_id
            
        except HttpError as e:
            print(f"❌ Erro no upload: {e}")
            return None
    
    def compartilhar_pasta(self, pasta_id, email_usuario):
        """
        Compartilha uma pasta com um usuário específico

        Args:
            pasta_id: ID da pasta a ser compartilhada
            email_usuario: Email do usuário para compartilhar
        """
        try:
            print(f"🔗 Compartilhando pasta com: {email_usuario}")

            permission = {
                'type': 'user',
                'role': 'writer',  # Permissão de escrita
                'emailAddress': email_usuario
            }

            self.service.permissions().create(
                fileId=pasta_id,
                body=permission,
                sendNotificationEmail=True
            ).execute()

            print(f"✅ Pasta compartilhada com sucesso!")

        except HttpError as e:
            print(f"⚠️  Erro ao compartilhar pasta: {e}")
            print("💡 A pasta foi criada, mas não foi possível compartilhar automaticamente")

    def configurar_estrutura_pastas(self, nome_projeto="Videos_Automatizados", email_usuario=None):
        """
        Configura a estrutura de pastas no Google Drive

        Args:
            nome_projeto: Nome da pasta principal do projeto
            email_usuario: Email para compartilhar a pasta (opcional)

        Returns:
            str: ID da pasta principal
        """
        try:
            print("🏗️  Configurando estrutura de pastas...")

            # Criar/verificar pasta principal
            self.pasta_principal_id = self.verificar_ou_criar_pasta(nome_projeto)

            if not self.pasta_principal_id:
                print("❌ Falha ao criar pasta principal")
                return None

            # Compartilhar pasta principal se email fornecido
            if email_usuario:
                self.compartilhar_pasta(self.pasta_principal_id, email_usuario)

            # Criar pasta com timestamp para esta execução
            timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
            pasta_execucao = f"Execucao_{timestamp}"
            pasta_execucao_id = self.verificar_ou_criar_pasta(pasta_execucao, self.pasta_principal_id)

            print(f"✅ Estrutura configurada! Pasta de execução: {pasta_execucao}")

            # Gerar link de acesso direto
            if self.pasta_principal_id:
                link_drive = f"https://drive.google.com/drive/folders/{self.pasta_principal_id}"
                print(f"🔗 Link direto: {link_drive}")

            return pasta_execucao_id

        except Exception as e:
            print(f"❌ Erro ao configurar estrutura: {e}")
            return None
    
    def fazer_upload_zip(self, caminho_zip, pasta_destino_id):
        """
        Faz upload de um arquivo ZIP específico
        
        Args:
            caminho_zip: Caminho para o arquivo ZIP
            pasta_destino_id: ID da pasta de destino
            
        Returns:
            bool: True se sucesso, False se erro
        """
        if not os.path.exists(caminho_zip):
            print(f"❌ Arquivo ZIP não encontrado: {caminho_zip}")
            return False
        
        arquivo_id = self.fazer_upload_arquivo(caminho_zip, pasta_destino_id)
        return arquivo_id is not None


def fazer_upload_todos_zips(pasta_rodada, nome_projeto="Videos_Automatizados", email_usuario=None):
    """
    Faz upload de todos os arquivos ZIP de uma rodada para o Google Drive

    Args:
        pasta_rodada: Pasta da rodada (ex: 2025_07_08_22_21_06)
        nome_projeto: Nome da pasta principal no Drive
        email_usuario: Email para compartilhar a pasta (ex: "<EMAIL>")

    Returns:
        bool: True se todos os uploads foram bem-sucedidos
    """
    
    print("☁️  INICIANDO UPLOAD PARA GOOGLE DRIVE")
    print("=" * 60)
    
    # Inicializar uploader
    uploader = GoogleDriveUploader()
    
    # Autenticar
    if not uploader.autenticar():
        print("❌ Falha na autenticação. Upload cancelado.")
        return False
    
    # Configurar estrutura de pastas
    pasta_execucao_id = uploader.configurar_estrutura_pastas(nome_projeto, email_usuario)
    if not pasta_execucao_id:
        print("❌ Falha ao configurar pastas. Upload cancelado.")
        return False
    
    # Buscar todos os arquivos ZIP
    zips_encontrados = []
    
    if not os.path.exists(pasta_rodada):
        print(f"❌ Pasta da rodada não encontrada: {pasta_rodada}")
        return False
    
    # Percorrer todas as pastas de vídeos
    for item in os.listdir(pasta_rodada):
        pasta_video = os.path.join(pasta_rodada, item)
        if os.path.isdir(pasta_video) and item.startswith('video_'):
            
            # Percorrer todas as pastas de idiomas
            for subitem in os.listdir(pasta_video):
                pasta_idioma = os.path.join(pasta_video, subitem)
                if os.path.isdir(pasta_idioma) and '_video_' in subitem:
                    
                    # Verificar pasta arquivos_finais
                    pasta_arquivos_finais = os.path.join(pasta_idioma, 'arquivos_finais')
                    if os.path.exists(pasta_arquivos_finais):
                        
                        # Buscar arquivos ZIP
                        for arquivo in os.listdir(pasta_arquivos_finais):
                            if arquivo.endswith('.zip'):
                                caminho_zip = os.path.join(pasta_arquivos_finais, arquivo)
                                zips_encontrados.append({
                                    'caminho': caminho_zip,
                                    'nome': arquivo,
                                    'video': item,
                                    'idioma': subitem.split('_')[-1]
                                })
    
    if not zips_encontrados:
        print("❌ Nenhum arquivo ZIP encontrado para upload")
        return False
    
    print(f"📦 Encontrados {len(zips_encontrados)} arquivos ZIP para upload")
    
    # Fazer upload de cada arquivo
    uploads_sucesso = 0
    uploads_total = len(zips_encontrados)
    
    for i, zip_info in enumerate(zips_encontrados, 1):
        print(f"\n📤 [{i}/{uploads_total}] Uploading: {zip_info['nome']}")
        print(f"   Vídeo: {zip_info['video']} | Idioma: {zip_info['idioma']}")
        
        sucesso = uploader.fazer_upload_zip(zip_info['caminho'], pasta_execucao_id)
        if sucesso:
            uploads_sucesso += 1
            print(f"✅ Upload concluído!")
        else:
            print(f"❌ Falha no upload!")
    
    # Resumo final
    print("\n" + "=" * 60)
    print("📊 RESUMO DO UPLOAD")
    print("=" * 60)
    print(f"✅ Uploads bem-sucedidos: {uploads_sucesso}")
    print(f"❌ Uploads falharam: {uploads_total - uploads_sucesso}")
    print(f"📁 Pasta no Drive: {nome_projeto}/Execucao_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}")
    
    return uploads_sucesso == uploads_total


# Teste da função (se executado diretamente)
if __name__ == "__main__":
    # Exemplo de uso
    pasta_teste = r"C:\Users\<USER>\OneDrive\Desktop\projeto\projetos\01_projeto_vingancas_avos\2025_07_08_22_21_06"
    if os.path.exists(pasta_teste):
        fazer_upload_todos_zips(pasta_teste)
    else:
        print("Pasta de teste não encontrada. Ajuste o caminho no código.")
