#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔄 FORÇAR RECRIAÇÃO DE ZIPS
===========================

Este script força a recriação de todos os arquivos ZIP, 
mesmo se eles já existirem.

Uso:
    python forcar_recriar_zips.py

Quando usar:
- Quando você fez mudanças nos arquivos finais
- Quando suspeita que algum ZIP está corrompido
- Quando quer garantir que todos os ZIPs estão atualizados
- Para resolver problemas de "ZIP já existe"

O que faz:
- Força recriação de TODOS os ZIPs da rodada atual
- Ignora verificações de "já existe"
- Recria mesmo ZIPs válidos
- Mantém backup dos ZIPs antigos (renomeia para .zip.bak)
"""

import os
import sys
from auxiliares.organizador_final import organizar_todos_idiomas

def encontrar_pasta_rodada_mais_recente():
    """Encontra a pasta de rodada mais recente"""
    pasta_projetos = os.path.join("projetos", "01_projeto_vingancas_avos")
    
    if not os.path.exists(pasta_projetos):
        return None
    
    # Listar todas as pastas de rodada
    pastas_rodada = []
    for item in os.listdir(pasta_projetos):
        caminho_item = os.path.join(pasta_projetos, item)
        if os.path.isdir(caminho_item) and item.replace("_", "").replace("-", "").isdigit():
            pastas_rodada.append((item, caminho_item))
    
    if not pastas_rodada:
        return None
    
    # Ordenar por nome (que é timestamp) e pegar a mais recente
    pastas_rodada.sort(reverse=True)
    return pastas_rodada[0][1]

def fazer_backup_zips(pasta_rodada):
    """Faz backup dos ZIPs existentes"""
    print("💾 Fazendo backup dos ZIPs existentes...")
    
    zips_encontrados = 0
    backups_criados = 0
    
    for item in os.listdir(pasta_rodada):
        pasta_video = os.path.join(pasta_rodada, item)
        if os.path.isdir(pasta_video) and item.startswith('video_'):
            
            for subitem in os.listdir(pasta_video):
                pasta_idioma = os.path.join(pasta_video, subitem)
                if os.path.isdir(pasta_idioma) and '_video_' in subitem:
                    
                    pasta_arquivos_finais = os.path.join(pasta_idioma, 'arquivos_finais')
                    if os.path.exists(pasta_arquivos_finais):
                        
                        for arquivo in os.listdir(pasta_arquivos_finais):
                            if arquivo.endswith('.zip'):
                                zips_encontrados += 1
                                caminho_zip = os.path.join(pasta_arquivos_finais, arquivo)
                                caminho_backup = caminho_zip + '.bak'
                                
                                try:
                                    # Remover backup antigo se existir
                                    if os.path.exists(caminho_backup):
                                        os.remove(caminho_backup)
                                    
                                    # Criar backup
                                    os.rename(caminho_zip, caminho_backup)
                                    backups_criados += 1
                                    print(f"   📦 Backup: {arquivo} -> {arquivo}.bak")
                                    
                                except Exception as e:
                                    print(f"   ⚠️  Erro ao fazer backup de {arquivo}: {e}")
    
    print(f"💾 Backup concluído: {backups_criados}/{zips_encontrados} arquivos")
    return backups_criados

def main():
    print("🔄 FORÇAR RECRIAÇÃO DE TODOS OS ZIPS")
    print("=" * 50)
    
    # Encontrar pasta de rodada
    if len(sys.argv) > 1:
        pasta_rodada = sys.argv[1]
        if not os.path.exists(pasta_rodada):
            print(f"❌ Pasta não encontrada: {pasta_rodada}")
            return
    else:
        pasta_rodada = encontrar_pasta_rodada_mais_recente()
        if not pasta_rodada:
            print("❌ Nenhuma pasta de rodada encontrada")
            print("💡 Use: python forcar_recriar_zips.py <caminho_da_pasta>")
            return
    
    print(f"📁 Pasta da rodada: {os.path.basename(pasta_rodada)}")
    print(f"📍 Caminho completo: {pasta_rodada}")
    
    # Confirmar ação
    resposta = input("\n⚠️  Isso irá RECRIAR TODOS os ZIPs. Continuar? (s/N): ").lower()
    if resposta not in ['s', 'sim', 'y', 'yes']:
        print("❌ Operação cancelada pelo usuário")
        return
    
    # Fazer backup dos ZIPs existentes
    backups_criados = fazer_backup_zips(pasta_rodada)
    
    # Forçar recriação
    print(f"\n🔄 Forçando recriação de todos os ZIPs...")
    print("=" * 50)
    
    try:
        organizar_todos_idiomas(pasta_rodada, forcar_recriar=True)
        
        print("\n" + "=" * 50)
        print("✅ RECRIAÇÃO CONCLUÍDA!")
        print("=" * 50)
        print(f"💾 Backups criados: {backups_criados}")
        print("📦 Todos os ZIPs foram recriados")
        print("🗑️  Para remover backups: del *.zip.bak")
        
    except Exception as e:
        print(f"\n❌ Erro durante a recriação: {e}")
        print("💾 Os backups estão disponíveis (.zip.bak)")
        print("🔧 Verifique os logs acima para mais detalhes")

if __name__ == "__main__":
    main()
