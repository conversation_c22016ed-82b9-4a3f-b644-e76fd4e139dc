#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Drive Pessoal Uploader - Upload direto para sua conta pessoal do Google Drive
"""

import os
import pickle
import webbrowser
from datetime import datetime
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.errors import HttpError


class DriveUploadPessoal:
    def __init__(self):
        self.SCOPES = ['https://www.googleapis.com/auth/drive']
        self.service = None
        self.creds = None
    
    def autenticar_conta_pessoal(self):
        """Autentica com sua conta pessoal do Google usando token salvo ou manual"""

        print("� AUTENTICAÇÃO PARA SUA CONTA PESSOAL")
        print("=" * 50)

        # Verificar se existe token salvo
        token_path = 'google_drive/token_pessoal.txt'

        if os.path.exists(token_path):
            print("🔍 Token salvo encontrado, tentando usar...")
            try:
                with open(token_path, 'r') as f:
                    saved_token = f.read().strip()

                if self.validar_token_salvo(saved_token):
                    print("✅ Token salvo ainda é válido!")
                    self.access_token = saved_token
                    self.configurar_servico_com_token(saved_token)
                    return True
                else:
                    print("⚠️  Token salvo expirou, obtendo novo...")
                    os.remove(token_path)  # Remove token expirado
            except Exception as e:
                print(f"⚠️  Erro ao ler token salvo: {e}")

        # Se não há token válido, fazer autenticação manual
        return self.autenticacao_manual()

    def validar_token_salvo(self, token):
        """Valida se um token salvo ainda é válido"""
        try:
            import requests
            headers = {'Authorization': f'Bearer {token}'}
            response = requests.get('https://www.googleapis.com/drive/v3/about?fields=user', headers=headers)
            return response.status_code == 200
        except:
            return False
    
    def autenticacao_manual(self):
        """Método de autenticação manual simplificado"""

        print("\n🔧 AUTENTICAÇÃO MANUAL SIMPLIFICADA")
        print("=" * 50)
        print("Vamos usar o OAuth Playground do Google para obter um token:")
        print("\n📋 PASSOS DETALHADOS:")
        print("1. 🌐 Abrindo OAuth Playground...")
        print("2. 📋 No lado esquerdo, procure por 'Drive API v3'")
        print("3. ✅ Marque: https://www.googleapis.com/auth/drive")
        print("4. 🔐 Clique em 'Authorize APIs'")
        print("5. 👤 Faça login com SUA CONTA PESSOAL")
        print("6. ✅ Autorize o acesso")
        print("7. 🔄 Clique em 'Exchange authorization code for tokens'")
        print("8. 📋 Copie o 'Access Token' (começa com ya29...)")

        # Abrir OAuth Playground automaticamente
        print("\n🌐 Abrindo OAuth Playground no seu navegador...")
        webbrowser.open('https://developers.google.com/oauthplayground')

        print("\n⏳ Aguardando você completar os passos acima...")
        input("Pressione ENTER quando estiver pronto para colar o token...")

        access_token = input("\n🔑 Cole o Access Token aqui: ").strip()

        if not access_token:
            print("❌ Token não fornecido")
            return False

        if not access_token.startswith('ya29'):
            print("⚠️  Token parece inválido (deve começar com 'ya29')")
            print("💡 Tente novamente copiando o token correto")
            return False

        try:
            print("🔍 Validando token...")

            # Testar token com requests simples
            import requests
            headers = {'Authorization': f'Bearer {access_token}'}
            response = requests.get('https://www.googleapis.com/drive/v3/about?fields=user,storageQuota', headers=headers)

            if response.status_code == 200:
                user_info = response.json()
                print(f"✅ Token válido!")
                print(f"👤 Usuário: {user_info['user']['displayName']}")
                print(f"📧 Email: {user_info['user']['emailAddress']}")

                # Mostrar espaço disponível
                if 'storageQuota' in user_info:
                    quota = user_info['storageQuota']
                    usado_gb = int(quota.get('usage', 0)) / (1024**3)
                    limite_gb = int(quota.get('limit', 0)) / (1024**3)
                    print(f"💾 Espaço usado: {usado_gb:.2f} GB")
                    if limite_gb > 0:
                        print(f"📊 Espaço total: {limite_gb:.2f} GB")
                        print(f"🆓 Espaço livre: {limite_gb - usado_gb:.2f} GB")

                # Salvar token para próximas execuções
                self.salvar_token(access_token)

                # Configurar serviço com token
                self.access_token = access_token
                self.configurar_servico_com_token(access_token)
                return True
            else:
                print(f"❌ Token inválido. Código: {response.status_code}")
                print(f"Resposta: {response.text}")
                return False

        except Exception as e:
            print(f"❌ Erro ao validar token: {e}")
            return False

    def configurar_servico_com_token(self, access_token):
        """Configura o serviço Google Drive com token manual"""

        # Criar classe de credenciais simples
        import google.auth.credentials
        from googleapiclient.discovery import build

        class TokenCredentials(google.auth.credentials.Credentials):
            def __init__(self, token):
                super().__init__()
                self.token = token

            def refresh(self, request):
                # Token manual não pode ser renovado automaticamente
                raise Exception("Token expirado. Execute novamente para obter novo token.")

            def apply(self, headers, token=None):
                headers['authorization'] = f'Bearer {self.token}'

        # Configurar serviço
        self.service = build('drive', 'v3', credentials=TokenCredentials(access_token))
        print("✅ Serviço Google Drive configurado!")

    def salvar_token(self, token):
        """Salva o token para reutilização futura"""
        try:
            os.makedirs('google_drive', exist_ok=True)
            token_path = 'google_drive/token_pessoal.txt'

            with open(token_path, 'w') as f:
                f.write(token)

            print(f"💾 Token salvo em: {token_path}")
            print("🔄 Nas próximas execuções, o token será reutilizado automaticamente!")

        except Exception as e:
            print(f"⚠️  Erro ao salvar token: {e}")
            print("💡 O token funcionará nesta execução, mas precisará ser inserido novamente na próxima")
    
    def criar_pasta(self, nome_pasta, pasta_pai_id=None):
        """Cria uma pasta no Drive"""
        try:
            file_metadata = {
                'name': nome_pasta,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            if pasta_pai_id:
                file_metadata['parents'] = [pasta_pai_id]
            
            pasta = self.service.files().create(body=file_metadata, fields='id').execute()
            return pasta.get('id')
            
        except Exception as e:
            print(f"❌ Erro ao criar pasta {nome_pasta}: {e}")
            return None
    
    def fazer_upload(self, caminho_arquivo, pasta_id=None):
        """Faz upload de um arquivo"""
        try:
            nome_arquivo = os.path.basename(caminho_arquivo)
            tamanho_mb = os.path.getsize(caminho_arquivo) / (1024 * 1024)
            
            print(f"📤 Uploading: {nome_arquivo} ({tamanho_mb:.2f} MB)")
            
            file_metadata = {'name': nome_arquivo}
            if pasta_id:
                file_metadata['parents'] = [pasta_id]
            
            media = MediaFileUpload(caminho_arquivo, resumable=True)
            
            request = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            )
            
            response = None
            while response is None:
                status, response = request.next_chunk()
                if status:
                    print(f"📊 Progress: {int(status.progress() * 100)}%")
            
            print(f"✅ Upload concluído! ID: {response.get('id')}")
            return response.get('id')
            
        except Exception as e:
            print(f"❌ Erro no upload de {nome_arquivo}: {e}")
            return None


def upload_para_drive_pessoal(pasta_rodada, nome_projeto="Videos_Vingancas_Avos"):
    """Faz upload para sua conta pessoal do Google Drive"""
    
    print("🏠 UPLOAD PARA SEU GOOGLE DRIVE PESSOAL")
    print("=" * 60)
    
    # Inicializar uploader
    uploader = DriveUploadPessoal()
    
    # Autenticar
    if not uploader.autenticar_conta_pessoal():
        print("❌ Falha na autenticação. Upload cancelado.")
        return False
    
    # Criar pasta principal
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    nome_pasta_principal = f"{nome_projeto}_{timestamp}"
    
    print(f"📁 Criando pasta: {nome_pasta_principal}")
    pasta_principal_id = uploader.criar_pasta(nome_pasta_principal)
    
    if not pasta_principal_id:
        print("❌ Falha ao criar pasta principal")
        return False
    
    # Buscar arquivos ZIP
    zips_encontrados = []
    
    for item in os.listdir(pasta_rodada):
        pasta_video = os.path.join(pasta_rodada, item)
        if os.path.isdir(pasta_video) and item.startswith('video_'):
            
            for subitem in os.listdir(pasta_video):
                pasta_idioma = os.path.join(pasta_video, subitem)
                if os.path.isdir(pasta_idioma) and '_video_' in subitem:
                    
                    pasta_arquivos_finais = os.path.join(pasta_idioma, 'arquivos_finais')
                    if os.path.exists(pasta_arquivos_finais):
                        
                        for arquivo in os.listdir(pasta_arquivos_finais):
                            if arquivo.endswith('.zip'):
                                caminho_zip = os.path.join(pasta_arquivos_finais, arquivo)
                                zips_encontrados.append(caminho_zip)
    
    if not zips_encontrados:
        print("❌ Nenhum arquivo ZIP encontrado")
        return False
    
    print(f"📦 Encontrados {len(zips_encontrados)} arquivos para upload")
    
    # Upload dos arquivos
    uploads_sucesso = 0
    
    for i, caminho_zip in enumerate(zips_encontrados, 1):
        print(f"\n📤 [{i}/{len(zips_encontrados)}]")
        arquivo_id = uploader.fazer_upload(caminho_zip, pasta_principal_id)
        if arquivo_id:
            uploads_sucesso += 1
    
    # Resultado final
    print(f"\n🎉 UPLOAD CONCLUÍDO!")
    print(f"✅ Sucessos: {uploads_sucesso}/{len(zips_encontrados)}")
    print(f"📁 Pasta criada: {nome_pasta_principal}")
    print(f"🔗 Acesse: https://drive.google.com/drive/folders/{pasta_principal_id}")
    
    return uploads_sucesso == len(zips_encontrados)


if __name__ == "__main__":
    # Teste
    pasta_teste = r"C:\Users\<USER>\OneDrive\Desktop\projeto\projetos\01_projeto_vingancas_avos\2025_07_08_22_21_06"
    if os.path.exists(pasta_teste):
        upload_para_drive_pessoal(pasta_teste)
    else:
        print("Pasta de teste não encontrada")
