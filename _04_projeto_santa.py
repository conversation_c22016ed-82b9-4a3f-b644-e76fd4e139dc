from auxiliares.auxiliar import (verificar_ou_criar_pasta, salvar_em_txt, ler_txt, limpar_texto, text_to_srt,
                      timestamp_com_underscore, listar_arquivos, listar_pastas,verifica_se_pasta_existe, concatenar_audios,divida_o_texto)

from auxiliares.api_ai_texto import executar_ai_texto
from auxiliares.proxy_funcional_5 import run as gerar_vozes
import os
from datetime import datetime


def gerar_roteiros(titulos, pasta_projeto, rodada: str = None):
        chave_arcee = '7PMJ0uWeAvzH4zMnVuOfT2EKRm0U6RO3YE5EKBrBwz8tciF47reDxornSS0vel_f6vgAWDo54ECjCsxGpCNUa5kIrNM'
        chave_google = 'AIzaSyBqywHOBq93SCLUF7QtZBamyzjTG2uhAvk'

        verificar_ou_criar_pasta(pasta_projeto)
        timestamp = timestamp_com_underscore()

        if rodada is None:
                rodada = timestamp

        pasta_rodada = os.path.join(pasta_projeto, rodada)
        verificar_ou_criar_pasta(pasta_rodada)


        for i, titulo in enumerate(titulos):
                
                # EXECUCAO PORTUGUÊS
                pasta_portugues = os.path.join(pasta_rodada, f"video_{i}", "video_pt")
                verificar_ou_criar_pasta(pasta_portugues)
                arquivos = listar_arquivos(pasta_portugues)
                # print(arquivos)
                if not '1_retorno_premissa.txt' in arquivos:
                        instrucao_premissa = ler_txt(pasta_projeto,'pt_instrucao_premissa.txt')
                        input_premissa = titulo
                        retorno_premissa  = executar_ai_texto('arcee','claude-3-7-sonnet-latest',instrucao_premissa,input_premissa,chave_arcee,'',1,10000)
                        salvar_em_txt(pasta_portugues,'1_retorno_premissa.txt',retorno_premissa)

                if not '2_retorno_roteirista_parte_1.txt' in arquivos:
                        instrucao_roteiro_parte_1 = ler_txt(pasta_projeto,'pt_instrucao_roteirista_parte_1.txt')
                        modelo_roteiro_1 = ler_txt(pasta_projeto,'pt_modelo_roteiro_1.txt')
                        input_roteiro_parte_1 = ler_txt(pasta_portugues,'1_retorno_premissa.txt')
                        retorno_roteiro_parte_1  = executar_ai_texto('arcee','claude-3-7-sonnet-latest',instrucao_roteiro_parte_1 + '\n' + modelo_roteiro_1   ,input_roteiro_parte_1,chave_arcee,'',0.4,11000)
                        salvar_em_txt(pasta_portugues,'2_retorno_roteirista_parte_1.txt',retorno_roteiro_parte_1)

                if not '3_retorno_roteirista_parte_2.txt' in arquivos:
                        instrucao_roteiro_parte_2 = ler_txt(pasta_projeto,'pt_instrucao_roteirista_parte_2.txt')
                        input_roteiro_parte_2 = ler_txt(pasta_portugues,'1_retorno_premissa.txt')+ '\n' +  ler_txt(pasta_portugues,'2_retorno_roteirista_parte_1.txt')
                        retorno_roteiro_parte_2  = executar_ai_texto('arcee','claude-3-7-sonnet-latest',instrucao_roteiro_parte_2,input_roteiro_parte_2,chave_arcee,'',0.4,11000)
                        salvar_em_txt(pasta_portugues,'3_retorno_roteirista_parte_2.txt',retorno_roteiro_parte_2)

                if not '4_roteiro_concatenado.txt' in arquivos:
                        roteiro_concatenado = ler_txt(pasta_portugues,'2_retorno_roteirista_parte_1.txt') + ler_txt(pasta_portugues,'3_retorno_roteirista_parte_2.txt')
                        salvar_em_txt(pasta_portugues,'4_roteiro_concatenado.txt',roteiro_concatenado)
                if not '100_roteiro_finalizado.txt' in arquivos:
                        roteiro_finalizado = ler_txt(pasta_portugues,'4_roteiro_concatenado.txt')
                        roteiro_finalizado = limpar_texto(roteiro_finalizado,['#'])
                        salvar_em_txt(pasta_portugues,'100_roteiro_finalizado.txt',roteiro_finalizado)

                roteiro_finaliado_português = ler_txt(pasta_portugues,'100_roteiro_finalizado.txt')

                # EXECUCAO ESPANHOL
                pasta_espanhol = os.path.join(pasta_rodada, f"video_{i}", "video_es")
                verificar_ou_criar_pasta(pasta_espanhol)
                arquivos = listar_arquivos(pasta_espanhol)
                # print(arquivos)
                if not '1_traducao_espanhol.txt' in arquivos: #gemini-2.5-flash-lite-preview-06-17
                        instrucao_traducao_es = ler_txt(pasta_projeto,'es_instrucao_traducao.txt')
                        input_traducao_es = roteiro_finaliado_português
                        textos_divididos = divida_o_texto(input_traducao_es,10000)
                        retorno_traducao_es = ''
                        for i_es, texto in enumerate(textos_divididos):
                                retorno_traducao_es_parte  = executar_ai_texto('google','gemini-2.5-flash-lite-preview-06-17',instrucao_traducao_es,texto,chave_google,'',0.4,62000,'')
                                salvar_em_txt(pasta_espanhol,f'1_traducao_espanhol_parte_{i_es}.txt',retorno_traducao_es_parte)
                                retorno_traducao_es = retorno_traducao_es + '\n' + retorno_traducao_es_parte
                        salvar_em_txt(pasta_espanhol,'1_traducao_espanhol.txt',retorno_traducao_es)
                        
                if not '2_naturalizacao_espanhol.txt' in arquivos:
                        instrucao_naturalizacao_es = ler_txt(pasta_projeto,'es_instrucao_naturalizacao.txt')
                        input_naturalizacao_es = ler_txt(pasta_espanhol,'1_traducao_espanhol.txt')
                        retorno_naturalizacao_es  = executar_ai_texto('google','gemini-2.5-flash',instrucao_naturalizacao_es,input_naturalizacao_es,chave_google,'',0.4,62000,'')
                        salvar_em_txt(pasta_espanhol,'2_naturalizacao_espanhol.txt',retorno_naturalizacao_es)
                if not '100_roteiro_finalizado.txt' in arquivos:
                        roteiro_finalizado = ler_txt(pasta_espanhol,'2_naturalizacao_espanhol.txt')
                        roteiro_finalizado = limpar_texto(roteiro_finalizado,['#'])
                        salvar_em_txt(pasta_espanhol,'100_roteiro_finalizado.txt',roteiro_finalizado)
                
                
                # EXECUCAO INGLES
                pasta_ingles = os.path.join(pasta_rodada, f"video_{i}", "video_en")
                verificar_ou_criar_pasta(pasta_ingles)
                arquivos = listar_arquivos(pasta_ingles)
                # print(arquivos)
                if not '1_traducao_ingles.txt' in arquivos:
                        instrucao_traducao_en = ler_txt(pasta_projeto,'en_instrucao_traducao.txt')
                        input_traducao_en = roteiro_finaliado_português
                        # tentativas = 0
                        # retorno_traducao_en = ""

                        # while len(retorno_traducao_en) < 40000 and tentativas < 5:
                        # retorno_traducao_en  = executar_ai_texto('google','gemini-2.5-flash',instrucao_traducao_en,input_traducao_en,chave_google,'',0.4,62000,'')
                                # print(len(retorno_traducao_en))
                                # tentativas += 1
                        # if len(retorno_traducao_en) < 40000:
                        #         raise ValueError('falou em 3 tentativas para traduzir para inglês')
                        
                        # retorno_traducao_en_parte  = executar_ai_texto('pollinations','openai-large',instrucao_traducao_en,input_traducao_en,'','',0.4,62000,'')
                        # salvar_em_txt(pasta_ingles,f'1_traducao_ingles_parte_teste.txt',retorno_traducao_en_parte)
                        # exit()


                        textos_divididos = divida_o_texto(input_traducao_en,10000)
                        retorno_traducao_en = ''
                        for i_en, texto in enumerate(textos_divididos):
                                retorno_traducao_en_parte  = executar_ai_texto('google','gemini-2.5-flash-lite-preview-06-17',instrucao_traducao_en,texto,chave_google,'',0.4,62000,'')
                                # retorno_traducao_en_parte  = executar_ai_texto('pollinations','openai-large',instrucao_traducao_en,texto,chave_google,'',0.4,62000,'')
                                salvar_em_txt(pasta_ingles,f'1_traducao_ingles_parte_{i_en}.txt',retorno_traducao_en_parte)
                                retorno_traducao_en = retorno_traducao_en + '\n' + retorno_traducao_en_parte
                       
                        salvar_em_txt(pasta_ingles,'1_traducao_ingles.txt',retorno_traducao_en)
                if not '2_naturalizacao_ingles.txt' in arquivos:
                        instrucao_naturalizacao_en = ler_txt(pasta_projeto,'en_instrucao_naturalizacao.txt')
                        input_naturalizacao_en = ler_txt(pasta_ingles,'1_traducao_ingles.txt')
                        retorno_naturalizacao_en  = executar_ai_texto('google','gemini-2.5-flash',instrucao_naturalizacao_en,input_naturalizacao_en,chave_google,'',0.4,62000,'')
                        salvar_em_txt(pasta_ingles,'2_naturalizacao_ingles.txt',retorno_naturalizacao_en)
                if not '100_roteiro_finalizado.txt' in arquivos:
                        roteiro_finalizado = ler_txt(pasta_ingles,'2_naturalizacao_ingles.txt')
                        roteiro_finalizado = limpar_texto(roteiro_finalizado,['#'])
                        salvar_em_txt(pasta_ingles,'100_roteiro_finalizado.txt',roteiro_finalizado)

        return pasta_rodada


# execução roteiro
####################################################################################################

nome_projeto = 'santa'
subpasta = '2025_06_25_01_26_46'
caminho_projeto = os.path.join(os.path.dirname(__file__), 'projetos', nome_projeto)

#2025_06_23_21_54_05
# titulos = ['A CASA DESABOU EM CIMA DO MEU FILHO… MAS A VIRGEM O TIROU DOS ESCOMBROS VIVO – HISTÓRIA DE FÉ']

#2025_06_24_01_36_23
# titulos = ['O CÂNCER JÁ ESTAVA TERMINAL… MAS UM CHÁ BENZIDO PELA VIRGEM SALVOU MINHA MÃE – HISTÓRIA DE FÉ',
#            'EU E MEU FILHO PASSAMOS 3 DIAS PERDIDOS NO MATO… ATÉ A VIRGEM NOS GUIAR COM UMA LUZ – HISTÓRIA DE FÉ']

#2025_06_24_02_06_02
# titulos = ['A Água Já Tava no Pescoço... Mas a Virgem Me Mostrou o Caminho na Enchente – HISTÓRIA DE FÉ',
#            'Incêndio Destruiu Tudo... Menos o Quadro da Virgem na Parede – HISTÓRIA DE FÉ',
#            'Os Médicos Me Deram 3 Horas de Vida... Mas a Virgem Chegou Primeiro – HISTÓRIA DE FÉ']

titulos  = ['Os Médicos Me Deram 3 Horas de Vida... Mas a Virgem Chegou Primeiro – HISTÓRIA DE FÉ']

pasta_rodada = gerar_roteiros(titulos, caminho_projeto, subpasta)
print(f'a pasta é: {pasta_rodada}')


# execução voz e concatenação
####################################################################################################
print('vai iniciar a geração de voz')
pastas = listar_pastas(pasta_rodada)
for pasta in pastas:
    subpastas = listar_pastas(os.path.join(pasta_rodada, pasta))
    for subpasta in subpastas:
        print(subpasta)
        if subpasta == "video_en":
                voiceid = 'EkK5I93UQWFDigLMpZcX'
        elif subpasta == "video_es":
                voiceid = 'EkK5I93UQWFDigLMpZcX'
        elif subpasta == "video_pt":
                voiceid = 'v3a2WxCpk7965Lwrexlc'
        else:
                raise ValueError(f'voice id não cadastrado para subpasta {subpasta}')
        caminho_subpasta = os.path.join(pasta_rodada, pasta, subpasta)
        print(caminho_subpasta)
        if not verifica_se_pasta_existe(caminho_subpasta+'/geracao_audios'):
                pasta_audios_gerados = verificar_ou_criar_pasta(caminho_subpasta+'/geracao_audios')
                print('vai executar e criar a pasta')
                roteiro = ler_txt(caminho_subpasta,'100_roteiro_finalizado.txt')
                frases = divida_o_texto(roteiro,1500)
                gerar_vozes(frases,6,voiceid,'eleven_multilingual_v2',pasta_audios_gerados)
        if not verifica_se_pasta_existe(caminho_subpasta+'/audio_gerado_concatenado'):
                concatenar_audios(pasta_audios_gerados,caminho_subpasta + '/audio_gerado_concatenado','audio_concatenado.wav')

# cria a pasta do video em branco
#########################################################
pastas = listar_pastas(pasta_rodada)
for pasta in pastas:
    subpastas = listar_pastas(os.path.join(pasta_rodada, pasta))
    for subpasta in subpastas:
        caminho_subpasta = os.path.join(pasta_rodada, pasta, subpasta)
        if not verifica_se_pasta_existe(caminho_subpasta+'/imagens'):
                pasta_audios_gerados = verificar_ou_criar_pasta(caminho_subpasta+'/imagens')
        if not verifica_se_pasta_existe(caminho_subpasta+'/video'):
                pasta_audios_gerados = verificar_ou_criar_pasta(caminho_subpasta+'/video')
            