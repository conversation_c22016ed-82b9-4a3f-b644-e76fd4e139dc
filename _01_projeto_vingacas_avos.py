from auxiliares.auxiliar import (verificar_ou_criar_pasta,embaralhar_e_renomear_videos, salvar_em_txt, ler_txt, limpar_texto, text_to_srt,remover_videos_corrompidos,
                      timestamp_com_underscore, listar_arquivos, listar_pastas,verifica_se_pasta_existe, extrair_dicionario_json,
                       salvar_base64_como_imagem,  concatenar_audios,divida_o_texto, criar_audio_mudo, concatenar_audios_melhorado)

from auxiliares.api_ai_texto import executar_ai_texto
from auxiliares.api_ai_text_to_image import executar_ai_imagem
from auxiliares.proxy_funcional_5 import run as gerar_vozes_elevenlabs
from auxiliares.gerador_thumbnail import executar_geracao_thumbnail
from auxiliares.deslocar_personagem import enquadrar_personagem_no_quadro as alinhar_personagem
from auxiliares.editar_imagem import editar_imagem
from auxiliares.remover_fundo import remover_fundo_e_salvar_com_corte, colocar_em_quadro
from auxiliares.busca_pixabay_pexels_api import search_videos
from auxiliares.renderizador_v2 import renderizar_video_completo as renderizar_video_completo_v2
from auxiliares.renderizador_v1 import renderizar_video_completo as renderizar_video_completo_v1
from auxiliares.organizador_final import organizar_todos_idiomas
from auxiliares.google_drive_uploader import fazer_upload_todos_zips
from auxiliares.drive_pessoal_uploader import upload_para_drive_pessoal
from auxiliares.validador_traducoes import ValidadorTraducoes, log_validacao

import os
import ast
from datetime import datetime
import json
import time
from multiprocessing import freeze_support


chave_arcee = '7PMJ0uWeAvzH4zMnVuOfT2EKRm0U6RO3YE5EKBrBwz8tciF47reDxornSS0vel_f6vgAWDo54ECjCsxGpCNUa5kIrNM'
chave_google = 'AIzaSyBqywHOBq93SCLUF7QtZBamyzjTG2uhAvk'
chave_opein_ai_lay = '********************************************************************************************************************************************************************'
assistent_imagem_thumbnail_lay = 'asst_1mQbukp6VRf5nPJOJi4BWtuQ'
chave_api_google_cloud = 'AIzaSyB3DO-hYCy-vqhD0D08l0BuioAzeBCdwu4'
chave_open_ai_juan = '********************************************************************************************************************************************************************'
assistent_gerador_frase_thumbnail_juan =  'asst_jfC3fpuxMFKs5hN6fHlEZBVi'
assistent_imagem_thumbnail_juan = 'asst_tiREqjFPlosXDQKhWtytJzkL'



def gerar_thumbnail(pasta_projeto,pasta_video, prompt_geracao_imagem,texto_thumbnail):

        font_file = os.path.join(pasta_projeto,'barlow-bold.ttf')
        # models/imagen-4.0-generate-preview-06-06 
        #FAZ A BUSCA DA IMAGEM PERSONAGEM EM 16:9
        pasta_thumbnail = verificar_ou_criar_pasta(pasta_video+'/imagem_thumbnail')
        if not verifica_se_pasta_existe(os.path.join(pasta_thumbnail,'01_busca_imagem_personagem')):
                pasta_busca_imagens = verificar_ou_criar_pasta(os.path.join(pasta_thumbnail,'01_busca_imagem_personagem'))
                imagens = executar_ai_imagem('google','models/imagen-3.0-generate-002',prompt_geracao_imagem,chave_api_google_cloud,'',1,'16:9')
                for i , imagem in enumerate(imagens):
                        caminho_imagem = salvar_base64_como_imagem(imagem,pasta_busca_imagens,f'imagem_personagem_{str(i)}.jpg')
        pasta_busca_imagens = verificar_ou_criar_pasta(os.path.join(pasta_thumbnail,'01_busca_imagem_personagem'))
        
        #GERA uma nova imagem alinha a esquerda para thumbnail
        if not verifica_se_pasta_existe(os.path.join(pasta_thumbnail,'02_imagem_thumbnail')):
                pasta_imagem_para_thumbnail = verificar_ou_criar_pasta(os.path.join(pasta_thumbnail,'02_imagem_thumbnail'))
                alinhar_personagem(os.path.join(pasta_busca_imagens,'imagem_personagem_0.jpg'),os.path.join(pasta_imagem_para_thumbnail,'imagem_01.jpg'),dimensoes_quadro=(1920, 1080),lado="direita", margem_adicional=-300)
        pasta_imagem_para_thumbnail = verificar_ou_criar_pasta(os.path.join(pasta_thumbnail,'02_imagem_thumbnail'))
        editar_imagem(os.path.join(pasta_imagem_para_thumbnail,'imagem_01.jpg'),os.path.join(pasta_imagem_para_thumbnail,'imagem_02_editada.jpg'),1,1.3,1.3,1.1,1)

        imagens_para_thumbnail = listar_arquivos(pasta_imagem_para_thumbnail)
        for i , imagem in enumerate(imagens_para_thumbnail):
                config_thumbnail = {
                'frame': {'width': 1920, 'height': 1080, 'background_color': '#000000'},
                'layers': [
                {
                        'type': 'solid_color',
                        'width_percent': 1.0, 'height_percent': 1.0,
                        'initial_alignment': 'top_left',
                        'gradient': {
                        'start_color': "#040404", 'end_color': '#000000',
                        'direction': 'left_to_right',
                        'start_percent': 0.6, 'end_percent': 0.75
                        }
                },
                {
                        'type': 'image', 'path': os.path.join(pasta_imagem_para_thumbnail,imagem),
                        'initial_alignment': 'bottom_right',
                        'resize_percent': 1,
                        'opacity': 1,
                        'brightness': 1.2,
                        'margin': {'bottom': 0}
                },

                {
                        'type': 'solid_color',
                        'width_percent': 1, 'height_percent': 1.0,
                        'initial_alignment': 'top_left',
                        'gradient': {
                        'start_color': "#040404", 'end_color': "#0C060600",
                        'direction': 'left_to_right',
                        'start_percent': 0.71, 'end_percent': 0.78
                        }
                },
                {
                        'type': 'text',
                        'text': texto_thumbnail.upper()  ,
                        # 'text': 'PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE%  *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE%  *SÓLIDA* OU EM %GRADIENTE% PADRÃO !DESTAQUE! E SOBRESCRITA *SÓLIDA* OU EM %GRADIENTE%',
                        'font_path': font_file, 'font_size': 170,
                        'initial_alignment': 'top_left',
                        'width_percent': 0.7, 'height_percent': 1,
                        'padding': {'top': 40, 'bottom': 40, 'left': 40, 'right': 30},
                        'auto_fit': True, 'min_font_size': 40, 'line_spacing': 1,
                        'horizontal_align': 'center', 'vertical_align': 'middle',
                        # 'gradient': {'scope': 'letter', 'start_color': "#F5F5F5", 'end_color': "#048462"},
                        'color_rules': {
                        # '*': {'color': '#FFFF00'},
                        # '%': {'gradient': {'scope': 'word', 'start_color': '#00F260', 'end_color': '#0575E6'}},
                        # '!': {'highlighter': {'color': '#FF0000', 'padding_x': 15, 'padding_y': 15}}
                        # },
                       
                        # 'alternating_lines': {
                        # 'gradient1': {
                        #         'start_color': '#FFFF00',
                        #         'end_color': '#FF8800',
                        #         'direction': 'horizontal'
                        # },
                        # 'gradient2': {
                        #         'start_color': "#053052",
                        #         'end_color': "#D82398",
                        #         'direction': 'horizontal'
                        # },    # Misturando gradiente e cor sólida
                        },

                        'first_lines_color': {
                    'count': 3,
                    'gradient': {            # Gradiente quente para primeiras
                        'start_color': "#FFF700C5",
                        'end_color': '#FF8800',
                        'direction': 'horizontal'
                    },
                    'remaining_gradient': {  # Gradiente frio para resto
                        'start_color': "#FFFFFF",
                        'end_color': "#F3F3F3",
                        'direction': 'horizontal'
                    }
                }

                },
                # {
                #         'type': 'text',
                #         'text': 'frase 2  frase 2  frase 2  frase 2  frase 2  frase 2 ',
                #         'font_path': font_file, 'font_size': 70,
                #         'initial_alignment': 'bottom_left',
                #         'width_percent': 0.75, 'height_percent': 0.15,
                #         'padding': {'top': 20, 'bottom': 20, 'left': 30, 'right': 30},
                #         'auto_fit': True, 'min_font_size': 40, 'line_spacing': 10,
                #         'horizontal_align': 'left', 'vertical_align': 'middle',
                #         'gradient': {'scope': 'word', 'start_color': '#7FFFD4', 'end_color': '#7FFFD4'},
                #         'color_rules': {
                #         '*': {'color': '#FFFF00'},
                #         '%': {'gradient': {'scope': 'word', 'start_color': '#00F260', 'end_color': '#0575E6'}},
                #         '!': {'highlighter': {'color': '#000000CC', 'padding_x': 15, 'padding_y': 5}}
                #         },
                #         'shadow': {'color': '#000000', 'offset_x': 8, 'offset_y': 8, 'blur': 3},
                #         'outline': {'color': '#000000', 'width': 1}
                # }
                ]
        }

                executar_geracao_thumbnail(config_thumbnail,pasta_video,f'thumbnail_{str(i)}.png')




def gerar_roteiros(idiomas, i , pasta_projeto, pasta_rodada):
        # print(idiomas)
        titulo_pt = idiomas['pt']['titulo']

        # Inicializar validador de traduções (apenas tamanho)
        validador = ValidadorTraducoes("")  # Sem Gemini, apenas validação de tamanho
   
        # EXECUCAO PORTUGUÊS
        pasta_portugues = os.path.join(pasta_rodada, f"video_{i}", "1_video_pt")
        verificar_ou_criar_pasta(pasta_portugues)
        arquivos = listar_arquivos(pasta_portugues)
        if not '0_titulo.txt' in arquivos:
                salvar_em_txt(pasta_portugues,'0_titulo.txt',titulo_pt)
        # print(arquivos)
        if not '1_retorno_premissa.txt' in arquivos:
                instrucao_premissa = ler_txt(pasta_projeto,'pt_instrucao_premissa.txt')
                input_premissa = titulo_pt
                retorno_premissa  = executar_ai_texto('arcee','claude-3-7-sonnet-latest',instrucao_premissa,input_premissa,chave_arcee,'',0.4,10000)
                salvar_em_txt(pasta_portugues,'1_retorno_premissa.txt',retorno_premissa)
        
        if not '2_retorno_roteirista_parte_1.txt' in arquivos:
                instrucao_roteiro_parte_1 = ler_txt(pasta_projeto,'pt_instrucao_roteirista_parte_1.txt')
                modelo_roteiro_1 = ler_txt(pasta_projeto,'pt_modelo_roteiro_1.txt')
                input_roteiro_parte_1 = ler_txt(pasta_portugues,'1_retorno_premissa.txt')
                retorno_roteiro_parte_1  = executar_ai_texto('arcee','claude-3-7-sonnet-latest',instrucao_roteiro_parte_1 + '\n' + modelo_roteiro_1   ,input_roteiro_parte_1,chave_arcee,'',0.4,11000)
                salvar_em_txt(pasta_portugues,'2_retorno_roteirista_parte_1.txt',retorno_roteiro_parte_1)
        
        if not '3_retorno_roteirista_parte_2.txt' in arquivos:
                instrucao_roteiro_parte_2 = ler_txt(pasta_projeto,'pt_instrucao_roteirista_parte_2.txt')
                input_roteiro_parte_2 = ler_txt(pasta_portugues,'1_retorno_premissa.txt')+ '\n' +  ler_txt(pasta_portugues,'2_retorno_roteirista_parte_1.txt')
                retorno_roteiro_parte_2  = executar_ai_texto('arcee','claude-3-7-sonnet-latest',instrucao_roteiro_parte_2,input_roteiro_parte_2,chave_arcee,'',0.4,11000)
                salvar_em_txt(pasta_portugues,'3_retorno_roteirista_parte_2.txt',retorno_roteiro_parte_2)

        if not '4_roteiro_concatenado.txt' in arquivos:
                roteiro_concatenado = ler_txt(pasta_portugues,'2_retorno_roteirista_parte_1.txt') + ler_txt(pasta_portugues,'3_retorno_roteirista_parte_2.txt')
                salvar_em_txt(pasta_portugues,'4_roteiro_concatenado.txt',roteiro_concatenado)
        if not '10_roteiro_finalizado.txt' in arquivos:
                roteiro_finalizado = ler_txt(pasta_portugues,'4_roteiro_concatenado.txt')
                roteiro_finalizado = limpar_texto(roteiro_finalizado,['#','*','---'])
                salvar_em_txt(pasta_portugues,'10_roteiro_finalizado.txt',roteiro_finalizado)
        
        roteiro_finaliado_português = ler_txt(pasta_portugues,'10_roteiro_finalizado.txt')
        
        #PROMP GERACAO IMAGEM ASSISTENTE OPEN AI
        if not '90_retorno_prompt_geracao_imagens.txt' in arquivos:
                input_prompt_geracao_imagens = titulo_pt
                retorno_prompt_geracao_imagens  = executar_ai_texto('openai','o4-mini','',input_prompt_geracao_imagens,chave_open_ai_juan,'',1,10000,assistent_imagem_thumbnail_juan)
                retorno_prompt_geracao_imagens = json.loads(retorno_prompt_geracao_imagens)
                retorno_prompt_geracao_imagens = retorno_prompt_geracao_imagens["modelo"][0]["prompt_img"]
                retorno_prompt_geracao_imagens = limpar_texto(retorno_prompt_geracao_imagens,[])
                salvar_em_txt(pasta_portugues,'90_retorno_prompt_geracao_imagens.txt',retorno_prompt_geracao_imagens)
        prompt_geracao_imagem = ler_txt(pasta_portugues,'90_retorno_prompt_geracao_imagens.txt')
        
        #TEXTO DA THUMBNAIL ASSISTENTE OPEN AI
        if not '91_retorno_do_texto_da_thumbnail.txt' in arquivos:  
                retorno_texto_thumbnail = executar_ai_texto('openai','o4-mini','',titulo_pt,chave_open_ai_juan,'',1,10000,assistent_gerador_frase_thumbnail_juan)
                retorno_texto_thumbnail = retorno_texto_thumbnail.replace('>','')
                salvar_em_txt(pasta_portugues,'91_retorno_do_texto_da_thumbnail.txt',retorno_texto_thumbnail)
        texto_thumbnail= ler_txt(pasta_portugues,'91_retorno_do_texto_da_thumbnail.txt')
        
        gerar_thumbnail(pasta_projeto,pasta_portugues,prompt_geracao_imagem,texto_thumbnail)
        
        
        # # EXECUCAO EM OUTROS IDIOMAS
        idiomas_execucao = ['es','en','de','fr','it','nl','pl']
        for i_video, idioma in enumerate(idiomas_execucao,start=2):
                pasta_idioma = os.path.join(pasta_rodada, f"video_{i}", f"{i_video}_video_{idioma}")
                verificar_ou_criar_pasta(pasta_idioma)
                arquivos = listar_arquivos(pasta_idioma)
                titulo_idioma = idiomas[idioma]['titulo']
                if not '0_titulo.txt' in arquivos:
                        salvar_em_txt(pasta_idioma,'0_titulo.txt',titulo_idioma)

                if not f'1_traducao_{idioma}.txt' in arquivos:
                        print(f"🔄 Iniciando tradução para {idioma}")

                        # Tentar tradução com validação
                        max_tentativas_traducao = 3
                        tentativa_traducao = 0
                        traducao_valida = False

                        while not traducao_valida and tentativa_traducao < max_tentativas_traducao:
                                tentativa_traducao += 1
                                print(f"📝 Tentativa {tentativa_traducao}/{max_tentativas_traducao} - Tradução {idioma}")

                                try:
                                        instrucao_traducao_idioma = ler_txt(pasta_projeto,f'{idioma}_instrucao_traducao.txt')
                                        instrucao_roteiro_parte_1 = ler_txt(pasta_projeto,'pt_instrucao_roteirista_parte_1.txt')
                                        instrucao_roteiro_parte_2 = ler_txt(pasta_projeto,'pt_instrucao_roteirista_parte_2.txt')
                                        input_traducao_idioma = roteiro_finaliado_português
                                        textos_divididos = divida_o_texto(input_traducao_idioma,10000)
                                        retorno_traducao_idioma = ''

                                        for i_texto_dividido, texto in enumerate(textos_divididos):
                                                # Concatenar contexto das instruções do roteiro original
                                                prompt_completo = f"""CONTEXTO - INSTRUÇÕES DO ROTEIRO ORIGINAL:

INSTRUÇÃO ROTEIRO PARTE 1:
{instrucao_roteiro_parte_1}

INSTRUÇÃO ROTEIRO PARTE 2:
{instrucao_roteiro_parte_2}

TEXTO A TRADUZIR:
{texto}"""

                                                retorno_traducao_idioma_parte = executar_ai_texto('google','gemini-2.5-flash',instrucao_traducao_idioma,prompt_completo,chave_google,'',0.4,62000,'')
                                                salvar_em_txt(pasta_idioma,f'1_traducao_{idioma}_parte_{i_texto_dividido}.txt',retorno_traducao_idioma_parte)
                                                retorno_traducao_idioma = retorno_traducao_idioma + '\n' + retorno_traducao_idioma_parte

                                        # Salvar tradução temporária para validação
                                        arquivo_traducao_temp = os.path.join(pasta_idioma, f'1_traducao_{idioma}_temp.txt')
                                        salvar_em_txt(pasta_idioma,f'1_traducao_{idioma}_temp.txt',retorno_traducao_idioma)

                                        # Validar tradução
                                        nome_pasta_video = f"{len([d for d in os.listdir(pasta_rodada) if d.startswith('video_')])}_video_{idioma}"
                                        traducao_valida, mensagem = validador.validar_traducao(
                                                roteiro_finaliado_português,
                                                arquivo_traducao_temp,
                                                nome_pasta_video
                                        )

                                        if traducao_valida:
                                                # Mover arquivo temporário para final
                                                salvar_em_txt(pasta_idioma,f'1_traducao_{idioma}.txt',retorno_traducao_idioma)
                                                os.remove(arquivo_traducao_temp)
                                                log_validacao(nome_pasta_video, "traducao", True, mensagem)
                                                print(f"✅ Tradução {idioma} validada com sucesso!")
                                        else:
                                                log_validacao(nome_pasta_video, "traducao", False, mensagem)
                                                print(f"❌ Tradução {idioma} inválida: {mensagem}")
                                                if tentativa_traducao < max_tentativas_traducao:
                                                        print(f"🔄 Tentando novamente...")

                                except Exception as e:
                                        print(f"❌ Erro na tradução {idioma}: {e}")
                                        traducao_valida = False

                        if not traducao_valida:
                                print(f"❌ FALHA: Não foi possível gerar tradução válida para {idioma} após {max_tentativas_traducao} tentativas")
                                raise Exception(f"Tradução para {idioma} falhou após {max_tentativas_traducao} tentativas")
                        
                if not f'2_naturalizacao_{idioma}.txt' in arquivos:
                        print(f"🎭 Iniciando naturalização para {idioma}")

                        # Tentar naturalização com validação
                        max_tentativas_naturalizacao = 3
                        tentativa_naturalizacao = 0
                        naturalizacao_valida = False

                        while not naturalizacao_valida and tentativa_naturalizacao < max_tentativas_naturalizacao:
                                tentativa_naturalizacao += 1
                                print(f"🎭 Tentativa {tentativa_naturalizacao}/{max_tentativas_naturalizacao} - Naturalização {idioma}")

                                try:
                                        instrucao_naturalizacao_idioma = ler_txt(pasta_projeto,f'{idioma}_instrucao_naturalizacao.txt')
                                        input_naturalizacao_idioma = ler_txt(pasta_idioma,f'1_traducao_{idioma}.txt')

                                        # Concatenar contexto do roteiro original
                                        prompt_naturalizacao_completo = f"""CONTEXTO - ROTEIRO ORIGINAL EM PORTUGUÊS:
{roteiro_finaliado_português}

TEXTO TRADUZIDO PARA NATURALIZAR:
{input_naturalizacao_idioma}"""

                                        retorno_naturalizacao_idioma = executar_ai_texto('google','gemini-2.5-flash',instrucao_naturalizacao_idioma,prompt_naturalizacao_completo,chave_google,'',0.4,62000,'')

                                        # Salvar naturalização temporária para validação
                                        arquivo_naturalizacao_temp = os.path.join(pasta_idioma, f'2_naturalizacao_{idioma}_temp.txt')
                                        salvar_em_txt(pasta_idioma,f'2_naturalizacao_{idioma}_temp.txt',retorno_naturalizacao_idioma)

                                        # Validar naturalização
                                        nome_pasta_video = f"{len([d for d in os.listdir(pasta_rodada) if d.startswith('video_')])}_video_{idioma}"
                                        naturalizacao_valida, mensagem = validador.validar_naturalizacao(
                                                roteiro_finaliado_português,
                                                arquivo_naturalizacao_temp,
                                                nome_pasta_video
                                        )

                                        if naturalizacao_valida:
                                                # Mover arquivo temporário para final
                                                salvar_em_txt(pasta_idioma,f'2_naturalizacao_{idioma}.txt',retorno_naturalizacao_idioma)
                                                os.remove(arquivo_naturalizacao_temp)
                                                log_validacao(nome_pasta_video, "naturalizacao", True, mensagem)
                                                print(f"✅ Naturalização {idioma} validada com sucesso!")
                                        else:
                                                log_validacao(nome_pasta_video, "naturalizacao", False, mensagem)
                                                print(f"❌ Naturalização {idioma} inválida: {mensagem}")
                                                if tentativa_naturalizacao < max_tentativas_naturalizacao:
                                                        print(f"🔄 Tentando novamente...")

                                except Exception as e:
                                        print(f"❌ Erro na naturalização {idioma}: {e}")
                                        naturalizacao_valida = False

                        if not naturalizacao_valida:
                                print(f"❌ FALHA: Não foi possível gerar naturalização válida para {idioma} após {max_tentativas_naturalizacao} tentativas")
                                raise Exception(f"Naturalização para {idioma} falhou após {max_tentativas_naturalizacao} tentativas")
                
                if not '10_roteiro_finalizado.txt' in arquivos:
                        print(f"📝 Criando roteiro finalizado para {idioma}")
                        roteiro_finalizado = ler_txt(pasta_idioma,f'2_naturalizacao_{idioma}.txt')
                        roteiro_finalizado = limpar_texto(roteiro_finalizado,['#'])
                        salvar_em_txt(pasta_idioma,'10_roteiro_finalizado.txt',roteiro_finalizado)
                        print(f"✅ Roteiro final {idioma} criado com sucesso!")


                #PROMP GERACAO IMAGEM ASSISTENTE OPEN AI
                if not '90_retorno_prompt_geracao_imagens.txt' in arquivos:
                        input_prompt_geracao_imagens = titulo_idioma
                        retorno_prompt_geracao_imagens  = executar_ai_texto('openai','o4-mini','',input_prompt_geracao_imagens,chave_open_ai_juan,'',1,10000,assistent_imagem_thumbnail_juan)
                        retorno_prompt_geracao_imagens = json.loads(retorno_prompt_geracao_imagens)
                        retorno_prompt_geracao_imagens = retorno_prompt_geracao_imagens["modelo"][0]["prompt_img"]
                        retorno_prompt_geracao_imagens = limpar_texto(retorno_prompt_geracao_imagens,[])
                        salvar_em_txt(pasta_idioma,'90_retorno_prompt_geracao_imagens.txt',retorno_prompt_geracao_imagens)
                prompt_geracao_imagem = ler_txt(pasta_idioma,'90_retorno_prompt_geracao_imagens.txt')
                
                #TEXTO DA THUMBNAIL ASSISTENTE OPEN AI
                if not '91_retorno_do_texto_da_thumbnail.txt' in arquivos:
                        input_texto_thumbnail = texto_thumbnail
                        print(input_texto_thumbnail)
                        instrucao_traducao_idioma = f"traduza para {idiomas[idioma]['idioma']} devolva somente a frase traduzida."
                        print(instrucao_traducao_idioma)
                        retorno_texto_thumbnail = executar_ai_texto('google','gemini-2.5-flash',instrucao_traducao_idioma,input_texto_thumbnail,chave_google,'',0.4,62000,'')
                        retorno_texto_thumbnail = retorno_texto_thumbnail.replace('>','')
                        salvar_em_txt(pasta_idioma,'91_retorno_do_texto_da_thumbnail.txt',retorno_texto_thumbnail)
                texto_thumbnail= ler_txt(pasta_idioma,'91_retorno_do_texto_da_thumbnail.txt')
                
                gerar_thumbnail(pasta_projeto,pasta_idioma,prompt_geracao_imagem,texto_thumbnail)


######## geracao de audios
def gerar_audios(pasta_rodada):
        pastas = listar_pastas(pasta_rodada)
        for pasta in pastas:
                subpastas = listar_pastas(os.path.join(pasta_rodada, pasta))
                for subpasta in subpastas:
                        print(subpasta)
                        # if subpasta == "3_video_en":
                        #         voiceid = ''
                        # elif subpasta == "2_video_es":
                        #         voiceid = ''
                        # elif subpasta == "1_video_pt":
                        #         voiceid = 'Ir1QNHvhaJXbAGhT50w3'
                        # else:
                        #         raise ValueError(f'voice id não cadastrado para subpasta {subpasta}')
                        voiceid = 'Ir1QNHvhaJXbAGhT50w3'
                        caminho_subpasta = os.path.join(pasta_rodada, pasta, subpasta)
                        print(caminho_subpasta)
                        if not verifica_se_pasta_existe(caminho_subpasta+'/geracao_audios'):
                                pasta_audios_gerados = verificar_ou_criar_pasta(caminho_subpasta+'/geracao_audios')
                                print('vai executar e criar a pasta')
                                roteiro = ler_txt(caminho_subpasta,'10_roteiro_finalizado.txt')
                                frases = divida_o_texto(roteiro,2500)
                                gerar_vozes_elevenlabs(frases,12,voiceid,'eleven_multilingual_v2',pasta_audios_gerados)
                        pasta_audios_gerados = verificar_ou_criar_pasta(caminho_subpasta+'/geracao_audios')
                        pasta_renderizacao = verificar_ou_criar_pasta(os.path.join(caminho_subpasta,'renderizacao'))
                        if not verifica_se_pasta_existe(pasta_renderizacao+'/0_narrador'):
                                concatenar_audios(pasta_audios_gerados,os.path.join(pasta_renderizacao,'0_narrador'),'audio_concatenado.mp3')

def obter_midias(pasta_rodada):
        itens_pasta_rodada = listar_pastas(pasta_rodada)
        for pasta in itens_pasta_rodada:
                caminho_pastas_videos = os.path.join(pasta_rodada,pasta)
                itens_pasta_videos = listar_pastas(caminho_pastas_videos)
                for pasta_video in itens_pasta_videos:
                        pasta_meu_video = os.path.join(caminho_pastas_videos,pasta_video)
                        pasta_renderizacao = os.path.join(pasta_meu_video,'renderizacao')
                        # remover_videos_corrompidos(pasta_renderizacao)
                        # print('chegou aqui')
                        # exit()
                        if verifica_se_pasta_existe(pasta_renderizacao):
                                criar_audio_mudo(6,os.path.join(pasta_renderizacao,'0_narrador'),)
                                #cria a pasta de arquivos de videos de fundo
                                if pasta_video == '1_video_pt':
                                        if not verifica_se_pasta_existe(os.path.join(pasta_renderizacao,'1_videos_fundo')):
                                                verificar_ou_criar_pasta(os.path.join(pasta_renderizacao,'1_videos_fundo'))
                                                search_videos('aerial 4k,mountain 4k,sunrise 4k,ocean 4k,forest 4k,leaves 4k,sunrise beautiful 4k,aerial beautiful 4k,nature 4k,sunrise beautiful 4k','720p',10,5,os.path.join(pasta_renderizacao,'1_videos_fundo'),True,True,False)
                                                remover_videos_corrompidos(pasta_renderizacao)

                                                
                                #cria a pasta do personagem
                                if not verifica_se_pasta_existe(os.path.join(pasta_renderizacao,'2_personagem')):
                                        verificar_ou_criar_pasta(os.path.join(pasta_renderizacao,'2_personagem'))
                                        remover_fundo_e_salvar_com_corte(os.path.join(pasta_meu_video,'imagem_thumbnail','01_busca_imagem_personagem','imagem_personagem_0.jpg'),os.path.join(pasta_renderizacao,'2_personagem','personagem_sem_fundo.png'),cor_fundo="#E6FF0A")
                                        # colocar_em_quadro(os.path.join(pasta_renderizacao,'2_personagem','personagem_sem_fundo.png'),os.path.join(pasta_renderizacao,'2_personagem','personagem_com_fundo.png'),cor_fundo='#00FF00')
                                        
                        else:
                                raise ValueError(f'pasta renderização não encontrada para {os.path.join(caminho_pastas_videos,pasta_video)}')


####################### RENDERIZACAO
def renderizar_videos(pasta_rodada):
        itens_pasta_rodada = listar_pastas(pasta_rodada)
        for pasta in itens_pasta_rodada:
                caminho_pastas_videos = os.path.join(pasta_rodada,pasta)
                itens_pasta_videos = listar_pastas(caminho_pastas_videos)
                for pasta_video in itens_pasta_videos:
                        print(pasta_video)
                        
                        pasta_renderizacao = os.path.join(caminho_pastas_videos,pasta_video,'renderizacao')
                        
                        pasta_videos_fundo = os.path.join(caminho_pastas_videos,'1_video_pt','renderizacao','1_videos_fundo','videos')
                        audio_fundo = os.path.abspath(os.path.join(pasta_rodada, '..', 'audio_fundo.mp3'))
                        efeito_overlay =  os.path.abspath(os.path.join(pasta_rodada, '..', 'efeitos_overlay',pasta_video[-2:]))
                        efeito_inscreva_se =  os.path.abspath(os.path.join(pasta_rodada, '..', 'efeitos_inscreva_se',pasta_video[-2:]))
               
                        embaralhar_e_renomear_videos(pasta_videos_fundo)
                        if verifica_se_pasta_existe(pasta_renderizacao):
                                sub_Pastas_renderizacao = listar_pastas(pasta_renderizacao)
                                verificar_pastas = ['0_narrador']
                                for check_pasta in verificar_pastas:
                                        if not check_pasta in sub_Pastas_renderizacao:
                                                raise ValueError(f'pasta {check_pasta} não encontrada para {os.path.join(caminho_pastas_videos,pasta_video)}')
                                        pasta_analisada = os.path.join(pasta_renderizacao,check_pasta)
                                        arquivos_na_pasta = listar_arquivos(pasta_analisada)
                                        if len(arquivos_na_pasta) == 0:
                                                raise ValueError(f'a pasta {pasta_analisada} não pode estar vazia')

                                print(f'vai renderizar {pasta_renderizacao}')
                                if not  verifica_se_pasta_existe(os.path.join(pasta_renderizacao,'99_video_base')):
                                        # #a renderização vai iniciar aqui:  
                                        inicio_execucao = time.time()
                                        
                                        # --- CONFIGURE SEU VÍDEO E PERFORMANCE AQUI ---
                                        config_legendas = {
                                                'ativar_legendas': False,  # <-- MUDE AQUI PARA False
                                                'fonte': 'LeagueSpartan-Bold', 
                                                'tamanho_fonte': 65,
                                                'posicao_vertical': 70,
                                                'max_palavras_por_linha': 8,
                                                'tamanho_destaque': 105,        
                                                'cor_fonte': "ffffff",
                                                'cor_destaque': "ffc600",
                                                'cor_contorno_degrade': '000000',
                                                'largura_video': 1280,
                                                'altura_video': 720,
                                        }

                                        config_ducking = {  
                                                'ativar': False, # <-- MUDE AQUI PARA False
                                                'threshold': 0.05,
                                                'ratio': 5,
                                                'attack': 0.1,
                                                'release': 0.8
                                        }

                                        config_audio = {
                                                'narracao': {'ativar': True, 'caminho': os.path.join(pasta_renderizacao,'0_narrador','audio_mudo.wav'), 'volume': 1.0},
                                                # 'fundo_1':  {'ativar': False, 'caminho': "audios/fundo1.wav", 'volume': 0.4},
                                                # 'fundo_2':  {'ativar': False, 'caminho': "audios/fundo2.wav", 'volume': 0.4},
                                        }

                                        config_camadas = [
                                                {
                                                'nome': 'A (Fundo)',
                                                'ativar_camada': True,
                                                # 'pasta': os.path.join(pasta_renderizacao,'1_videos_fundo','videos'),
                                                'pasta': pasta_videos_fundo,
                                                'duracao_imagem': 5,
                                                },
                                                {
                                                'nome': 'C (efeito)',
                                                'ativar_camada': True,
                                                'pasta': efeito_overlay,
                                                'duracao_imagem': 15,
                                                'remover_fundo': False, 'cor_remover': "FFFFFF", 'transparencia_alpha': 0.3,
                                                'ativar_brilho': False, 'valor_brilho': 0.5,
                                                'ativar_transicao': False,
                                                'ativar_movimento': False,
                                                'ativar_pausa_loop': False,
                                                },
                                                {
                                                'nome': 'B (Overlay 1)',
                                                'ativar_camada': True,
                                                'pasta': os.path.join(pasta_renderizacao,'2_personagem'),
                                                'duracao_imagem': 60,
                                                'remover_fundo': True,
                                                'similarity': 0.15,             # Tolerância melhorada
                                                'blend': 0.08,                  # Suavização melhorada
                                                'yuv': True,                    # Usar espaço YUV (recomendado)
                                                'suavizar_bordas': True,
                                                'cor_remover': "E6FF0A",
                                                'transparencia_alpha': 1,
                                                'ativar_brilho': False,
                                                'valor_brilho': 0.3,
                                                'ativar_movimento': True,
                                                'tipo_movimento': 'pendulo',  # Mudado para pêndulo
                                                'velocidade_movimento': 80,    # Velocidade do pêndulo
                                                # Configurações avançadas do pêndulo
                                                'config_pendulo': {
                                                        'tipo_pendulo': 'completo',        # 'simples', 'horizontal', 'vertical', 'completo'
                                                        'rotacao_max_graus': 2.0,          # Rotação máxima em graus
                                                        'amplitude_horizontal': 12,        # Amplitude horizontal
                                                        'amplitude_vertical': 6,           # Amplitude vertical
                                                        'zoom_base': 1.3                   # Zoom base para dar espaço
                                                },
                                                'ativar_pausa_loop': False,
                                                'duracao_pausa_loop': 0,
                                                'audio_transicao': {
                                                        'ativar': True,
                                                        'caminho': 'audios/transicao.mp3', # Pode ser um som diferente para cada camada!
                                                        'volume': 1,
                                                        'duracao_fade': 0.4
                                                }
                                                },
                                                
                                                # {
                                                # 'nome': 'D (personagem)',
                                                # 'ativar_camada': True,
                                                # 'pasta': "2-personagem",
                                                # 'remover_fundo': False,
                                                # 'cor_remover': "75F94D",
                                                # 'transparencia_alpha': 1.0,
                                                # 'similarity': 0.15,
                                                # 'blend': 0.08,
                                                # # Adiciona movimento de pêndulo suave para o personagem
                                                # 'ativar_movimento': True,
                                                # 'tipo_movimento': 'pendulo',
                                                # 'velocidade_movimento': 2,  # Movimento mais suave para personagem
                                                # 'config_pendulo': {
                                                #         'tipo_pendulo': 'horizontal',      # Apenas movimento horizontal
                                                #         'rotacao_max_graus': 0.8,          # Rotação sutil
                                                #         'amplitude_horizontal': 6,         # Movimento horizontal suave
                                                #         'amplitude_vertical': 0,           # Sem movimento vertical
                                                #         'zoom_base': 1.1                   # Zoom mínimo
                                                # },
                                                # },
                                                # {
                                                # 'nome': 'E (efeito)',
                                                # 'ativar_camada': False  ,
                                                # 'pasta': "1-efeito_principal",
                                                # 'duracao_imagem': 15,
                                                # 'remover_fundo': False, 'cor_remover': "", 'transparencia_alpha': 0.4,
                                                # 'ativar_brilho': False, 'valor_brilho': 0.9,
                                                # 'ativar_transicao': False,
                                                # 'ativar_movimento': False,
                                                # 'ativar_pausa_loop': False,
                                                # },
                                                {
                                                'nome': 'F (inscreva-se)',
                                                'ativar_camada': True,
                                                'pasta': efeito_inscreva_se,
                                                'duracao_imagem': 5,
                                                'remover_fundo': True, 'cor_remover': "75F94D", 'transparencia_alpha': 1,
                                                'ativar_brilho': False, 'valor_brilho': 0.9,
                                                'ativar_transicao': False,
                                                'ativar_movimento': False,
                                                'ativar_pausa_loop': True, 'duracao_pausa_loop': 400,
                                                },
                                        ]

                                        
                                        # Configurações otimizadas para GPU
                                        preset_de_renderizacao = 'p1'  # Preset mais rápido para GPU
                                        limite_de_processos_paralelos = 1  # GPU funciona melhor com menos processos paralelos
                                        
                                        # <<< CORREÇÃO: Parâmetro 'limite_processos' adicionado à definição da função >>>
                                        renderizar_video_completo_v2(
                                        config_camadas=config_camadas, 
                                        config_audio=config_audio,
                                        config_legenda=config_legendas,
                                        config_ducking=config_ducking, 
                                        output_folder=os.path.join(pasta_renderizacao,'99_video_base'), 
                                        tamanho="1280x720", 
                                        fps=30,
                                        preset_render=preset_de_renderizacao,
                                        limite_processos=limite_de_processos_paralelos
                                        )
                                        fim_execucao = time.time()
                                        duracao_total = fim_execucao - inicio_execucao
                                        minutos, segundos = divmod(duracao_total, 60)
                                        print("\n" + "="*50)
                                        print(f"⏱️ Tempo total de execução: {int(minutos)} minutos e {int(segundos)} segundos.")
                                        print("="*50)
                                        
                                        
                                
                                if not  verifica_se_pasta_existe(os.path.join(pasta_renderizacao,'100_video_renderizado')):
                                        # #a renderização vai iniciar aqui:  
                                        inicio_execucao = time.time()
                                        
                                        # --- CONFIGURE SEU VÍDEO E PERFORMANCE AQUI ---
                                        config_legendas = {
                                                'ativar_legendas': True,  # <-- MUDE AQUI PARA False
                                                'fonte': 'barlow-bold',
                                                'caminho_fonte': os.path.join(pasta_rodada,'..','barlow-bold.ttf'),
                                                'tamanho_fonte': 65,
                                                'posicao_vertical': 50,
                                                'max_palavras_por_linha': 8,
                                                'tamanho_destaque': 105,
                                                'cor_fonte': "FFFFFF",
                                                'cor_destaque': "e8fc3b",
                                                'cor_contorno_degrade': "000000",
                                                'largura_video': 1280,
                                                'altura_video': 720,
                                                'idioma': pasta_video[-2:],
                                                # Configurações otimizadas do Whisper para GPU
                                                'modelo_whisper': 'small',  # Modelo balanceado (base, small, medium, large)
                                                'usar_gpu': True,  # Forçar uso da GPU
                                                'compute_type': 'float16',  # FP16 para GPU (mais rápido)
                                                'beam_size': 1,  # Reduzir para mais velocidade
                                                'best_of': 1,  # Reduzir para mais velocidade
                                                'temperature': 0.0,  # Determinístico
                                                'condition_on_previous_text': False  # Melhor para paralelização
                                        }

                                        config_ducking = {  
                                                'ativar': False, # <-- MUDE AQUI PARA False
                                                'threshold': 0.05,
                                                'ratio': 5,
                                                'attack': 0.1,
                                                'release': 0.8
                                        }

                                        config_audio = {
                                                'narracao': {'ativar': True, 'caminho': os.path.join(pasta_renderizacao,'0_narrador','audio_concatenado.mp3'), 'volume': 1.0},
                                                'fundo_1':  {'ativar': True, 'caminho': audio_fundo, 'volume': 0.1},
                                                # 'fundo_2':  {'ativar': False, 'caminho': "audios/fundo2.wav", 'volume': 0.4},
                                        }

                                        config_camadas = [
                                                {
                                                'nome': 'A (Fundo)',
                                                'ativar_camada': True,
                                                'pasta': os.path.join(pasta_renderizacao,'99_video_base')
                                                }
                                                
                                        ]

                                        
                                        # Configurações otimizadas para GPU
                                        preset_de_renderizacao = 'p1'  # Preset mais rápido para GPU
                                        limite_de_processos_paralelos = 1  # GPU funciona melhor com menos processos paralelos
                                        
                                        # <<< CORREÇÃO: Parâmetro 'limite_processos' adicionado à definição da função >>>
                                        renderizar_video_completo_v1(
                                        config_camadas=config_camadas, 
                                        config_audio=config_audio,
                                        config_legenda=config_legendas,
                                        config_ducking=config_ducking, 
                                        output_folder=os.path.join(pasta_renderizacao,'100_video_renderizado'), 
                                        tamanho="1280x720", 
                                        fps=30,
                                        preset_render=preset_de_renderizacao,
                                        limite_processos=limite_de_processos_paralelos
                                        )
                                        fim_execucao = time.time()
                                        duracao_total = fim_execucao - inicio_execucao
                                        minutos, segundos = divmod(duracao_total, 60)
                                        print("\n" + "="*50)
                                        print(f"⏱️ Tempo total de execução: {int(minutos)} minutos e {int(segundos)} segundos.")
                                        print("="*50)
                                        
                                        
                        else:
                                raise ValueError(f'pasta renderização não encontrada para {os.path.join(caminho_pastas_videos,pasta_video)}')
         

# Execução
####################################################################################################
if __name__ == '__main__':
        freeze_support()
        nome_projeto = '01_projeto_vingancas_avos'
        pasta_projeto = os.path.join(os.path.dirname(__file__), 'projetos', nome_projeto)
        verificar_ou_criar_pasta(pasta_projeto)

        criar_novo_projeto  = input("digite 1 para criar novos titulos / 2 para continuar uma pasta criada:")
        if criar_novo_projeto == '1':
                print('vai criar novos titulos')
                quantidade = input('digite a quantidade de titulos:')
                retorno_da_chamada_de_titulos = retorno_prompt_geracao_imagens  = executar_ai_texto('openai','o4-mini','',f'me de {quantidade} titulo',chave_open_ai_juan,'',1,10000,'asst_9jq1BaodXr74zSjYVhEHwqhP')
                timestamp = timestamp_com_underscore()
                pasta_rodada = os.path.join(pasta_projeto, timestamp)
                verificar_ou_criar_pasta(pasta_rodada)
                salvar_em_txt(pasta_rodada,'titulos.txt',retorno_da_chamada_de_titulos)

        elif criar_novo_projeto == '2':
                nome_da_pasta = input('digite a pasta:')
                pasta_rodada = os.path.join(pasta_projeto, nome_da_pasta)
                if not verifica_se_pasta_existe(pasta_rodada):
                        print(f'a pasta {nome_da_pasta} não existe')
                        exit()

        else:
                print('seleção não cadastrada')
                exit()
        

        
        caminho_titulos = os.path.join(pasta_rodada,'titulos.txt')
        try:
                dicionario = extrair_dicionario_json(caminho_titulos)
        except:
                dicionario = ler_txt(caminho_titulos)


        for indice, (chave_titulo, dados_titulo) in enumerate(dicionario.items(), start=1):
                idiomas = dados_titulo['idiomas']
                gerar_roteiros(idiomas,indice, pasta_projeto, pasta_rodada)

                #exit()  # Para antes da geração de áudio
                gerar_audios(pasta_rodada)
                obter_midias(pasta_rodada)
                renderizar_videos(pasta_rodada)

        # Organizar arquivos finais após toda a renderização
        print("\n" + "="*60)
        print("📦 ORGANIZANDO ARQUIVOS FINAIS")
        print("="*60)
        organizar_todos_idiomas(pasta_rodada)

        # Upload para SEU Google Drive pessoal
        print("\n" + "="*60)
        print("🏠 UPLOAD PARA SEU GOOGLE DRIVE PESSOAL")
        print("="*60)
        nome_projeto = f"Videos_Vingancas_Avos_{os.path.basename(pasta_rodada)}"

        sucesso_upload = upload_para_drive_pessoal(pasta_rodada, nome_projeto)

        if sucesso_upload:
            print("🎉 Todos os arquivos foram enviados para SEU Google Drive pessoal!")
            print("📁 Os vídeos estão agora diretamente na sua conta!")
        else:
            print("⚠️  Alguns arquivos podem não ter sido enviados. Verifique os logs acima.")
