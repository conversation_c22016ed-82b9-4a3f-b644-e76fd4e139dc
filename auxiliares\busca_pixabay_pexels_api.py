import requests
import random
import os
import json
import asyncio
import aiohttp
import aiofiles


def expandir_termos_busca(termos_base):
    """Expande termos de busca com sinônimos e variações"""

    # Dicionário de expansões por categoria
    expansoes = {
        'aerial': ['drone', 'sky', 'flying', 'bird view', 'overhead', 'top view', 'helicopter', 'clouds'],
        'mountain': ['hills', 'peaks', 'landscape', 'valley', 'cliff', 'rock', 'stone', 'nature'],
        'sunrise': ['sunset', 'dawn', 'dusk', 'golden hour', 'morning', 'evening', 'light', 'sun'],
        'ocean': ['sea', 'water', 'waves', 'beach', 'coast', 'shore', 'blue', 'marine'],
        'forest': ['trees', 'woods', 'jungle', 'green', 'leaves', 'nature', 'wilderness'],
        'city': ['urban', 'buildings', 'street', 'traffic', 'lights', 'downtown', 'skyline'],
        'abstract': ['motion', 'particles', 'flowing', 'smooth', 'gradient', 'texture'],
        'fire': ['flames', 'burning', 'heat', 'orange', 'red', 'smoke'],
        'water': ['rain', 'drops', 'flowing', 'river', 'stream', 'liquid'],
        'space': ['stars', 'galaxy', 'universe', 'cosmic', 'nebula', 'dark']
    }

    termos_expandidos = []

    for termo in termos_base:
        termo = termo.strip().lower()
        termos_expandidos.append(termo)  # Termo original

        # Adicionar expansões se existirem
        if termo in expansoes:
            # Pegar 2-3 sinônimos aleatórios
            sinonimos = random.sample(expansoes[termo], min(3, len(expansoes[termo])))
            termos_expandidos.extend(sinonimos)

        # Adicionar variações genéricas
        variacoes_genericas = [
            f"{termo} nature",
            f"{termo} beautiful",
            f"{termo} cinematic",
            f"{termo} 4k",
            f"amazing {termo}",
            f"stunning {termo}"
        ]

        # Adicionar 1-2 variações aleatórias
        termos_expandidos.extend(random.sample(variacoes_genericas, 2))

    # Embaralhar e retornar lista única
    termos_expandidos = list(set(termos_expandidos))  # Remove duplicatas
    random.shuffle(termos_expandidos)

    return termos_expandidos


def gerar_termos_aleatorios_por_categoria():
    """Gera termos completamente aleatórios por categoria para máxima diversidade"""

    categorias = {
        'natureza': ['aerial', 'mountain', 'sunrise', 'sunset', 'ocean', 'forest', 'river', 'lake', 'desert', 'valley'],
        'urbano': ['city', 'street', 'traffic', 'buildings', 'lights', 'downtown', 'highway', 'bridge'],
        'abstrato': ['abstract', 'motion', 'particles', 'flowing', 'gradient', 'texture', 'smoke', 'fire'],
        'clima': ['rain', 'snow', 'storm', 'clouds', 'wind', 'fog', 'lightning'],
        'agua': ['water', 'waves', 'drops', 'flowing', 'splash', 'underwater'],
        'espaco': ['space', 'stars', 'galaxy', 'cosmic', 'nebula', 'planet']
    }

    # Selecionar 2-3 categorias aleatórias
    categorias_selecionadas = random.sample(list(categorias.keys()), random.randint(2, 3))

    termos_finais = []
    for categoria in categorias_selecionadas:
        # Pegar 1-2 termos de cada categoria
        termos_categoria = random.sample(categorias[categoria], random.randint(1, 2))
        termos_finais.extend(termos_categoria)

    return termos_finais

def search_videos(queries, qualidade="1080p", duracao_maxima_segundos=60,
                                    duracao_total_min=10, pasta_saida="downloads",
                                    incluir_pixabay=True, incluir_pexels=True,
                                    expandir_busca=True,
                                    pixabay_api_key='**********************************',
                                    pexels_api_key='5qcDCUqYMQokzZptN92txcypVskpsIzArYh6z4LYHEZQCoI6BUJjdncI'):

    queries_originais = [q.strip() for q in queries.split(",") if q.strip()]

    # Expandir termos se solicitado
    if expandir_busca:
        queries_expandidas = expandir_termos_busca(queries_originais)
        # Limitar a 15 termos para evitar timeout
        queries_expandidas = queries_expandidas[:15]
        print(f"🔍 Termos originais: {queries_originais}")
        print(f"🚀 Termos expandidos: {len(queries_expandidas)} termos (limitado a 15)")
        queries = queries_expandidas
    else:
        queries = queries_originais

    os.makedirs(os.path.join(pasta_saida, "videos"), exist_ok=True)
    todos_videos = []
    urls_vistas = set()  # Para evitar vídeos duplicados

    quality_map_pixabay = {"1080p": "large", "720p": "medium", "360p": "small", "tiny": "tiny"}
    quality_map_pexels = {
        "360p": (640, 360), "720p": (1280, 720), "1080p": (1920, 1080),
        "1440p": (2560, 1440), "2160p": (3840, 2160)
    }
    resolucao_alvo = quality_map_pexels.get(qualidade, (1920, 1080))
    qualidade_pixabay = quality_map_pixabay.get(qualidade, qualidade)

    # Busca síncrona (requests) para coletar vídeos
    for termo in queries:
        termo_query = termo.replace(";", "+").replace(",", "+").replace(" ", "+")

        if incluir_pixabay:
            # Páginas aleatórias para mais diversidade (reduzido para evitar timeout)
            paginas_aleatorias = random.sample(range(1, 6), min(2, 5))  # 2 páginas aleatórias de 1-5
            for page in paginas_aleatorias:
                url = f"https://pixabay.com/api/videos/?key={pixabay_api_key}&q={termo_query}&per_page=20&page={page}"
                try:
                    print(f"🔍 Pixabay: {termo} (página {page})")
                    r = requests.get(url, timeout=15)
                    if r.status_code == 200:
                        hits = r.json().get("hits", [])
                        for video in hits:
                            duration = video.get("duration", 0)
                            if duration <= duracao_maxima_segundos and qualidade_pixabay in video["videos"]:
                                v = video["videos"][qualidade_pixabay]
                                w, h = v.get("width"), v.get("height")
                                if not w or not h:
                                    continue
                                proporcao = w / h
                                if not (1.72 <= proporcao <= 1.78):
                                    continue

                                # Verificar se URL já foi vista (evitar duplicatas)
                                if v["url"] not in urls_vistas:
                                    urls_vistas.add(v["url"])
                                    todos_videos.append({
                                        "video_url": v["url"],
                                        "duration": duration,
                                        "query": termo,
                                        "source": "pixabay"
                                    })
                    else:
                        print(f"[Pixabay] Erro: {r.status_code}")
                except Exception as e:
                    print(f"❌ Erro Pixabay {termo} página {page}: {e}")
                    continue  # Pula para próxima página

        if incluir_pexels:
            headers = {"Authorization": pexels_api_key}
            # Páginas aleatórias para mais diversidade (reduzido para evitar timeout)
            paginas_aleatorias = random.sample(range(1, 11), min(3, 10))  # 3 páginas aleatórias de 1-10
            for page in paginas_aleatorias:
                params = {"query": termo, "per_page": 80, "page": page}
                try:
                    print(f"🎬 Pexels: {termo} (página {page})")
                    r = requests.get("https://api.pexels.com/videos/search", headers=headers, params=params, timeout=15)
                    if r.status_code == 200:
                        for video in r.json().get("videos", []):
                            duration = video.get("duration", 0)
                            if duration <= duracao_maxima_segundos:
                                best_file = None
                                min_diff = float('inf')
                                for file in video.get("video_files", []):
                                    w, h = file.get("width"), file.get("height")
                                    if not w or not h:
                                        continue
                                    proporcao = w / h
                                    if not (1.72 <= proporcao <= 1.78):
                                        continue
                                    diff = abs(w - resolucao_alvo[0]) + abs(h - resolucao_alvo[1])
                                    if diff < min_diff:
                                        best_file = file
                                        min_diff = diff
                                if best_file:
                                    # Verificar se URL já foi vista (evitar duplicatas)
                                    if best_file["link"] not in urls_vistas:
                                        urls_vistas.add(best_file["link"])
                                        todos_videos.append({
                                            "video_url": best_file["link"],
                                            "duration": duration,
                                            "query": termo,
                                            "source": "pexels"
                                        })
                    else:
                        print(f"[Pexels] Erro: {r.status_code}")
                except Exception as e:
                    print(f"[Pexels] Exceção: {e}")

    # Seleção aleatória por duração total
    random.shuffle(todos_videos)
    selecionados, acumulado = [], 0
    for vid in todos_videos:
        if acumulado >= duracao_total_min * 60:
            break
        selecionados.append(vid)
        acumulado += vid["duration"]

    if not selecionados:
        print("❌ Nenhum vídeo válido encontrado.")
        return []

    # Salvar JSON
    json_path = os.path.join(pasta_saida, "retorno.json")
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(selecionados, f, ensure_ascii=False, indent=4)

    # Download assíncrono
    asyncio.run(baixar_todos_videos_async(selecionados, pasta_saida))

    print(f"🚀 Processo concluído. Total: {len(selecionados)} vídeos ({acumulado // 60} min)")
    return selecionados

async def baixar_video_async(session, i, item, pasta_saida, total):
    url = item["video_url"]
    nome_base = item["query"].replace("/", "-")
    filename = f"{i:03d} - {nome_base}.mp4"
    path = os.path.join(pasta_saida, "videos", filename)
    try:
        print(f"🔽 [{i}/{total}] Baixando: {filename}")
        async with session.get(url, timeout=60) as resp:
            resp.raise_for_status()
            f = await aiofiles.open(path, mode='wb')
            async for chunk in resp.content.iter_chunked(8192):
                await f.write(chunk)
            await f.close()
        print(f"✅ Concluído: {filename}")
        return True
    except Exception as e:
        print(f"❌ Erro ao baixar {filename}: {e}")
        return False

async def baixar_todos_videos_async(selecionados, pasta_saida, max_concurrent=10):
    os.makedirs(os.path.join(pasta_saida, "videos"), exist_ok=True)
    total = len(selecionados)
    connector = aiohttp.TCPConnector(limit=max_concurrent)
    timeout = aiohttp.ClientTimeout(total=120)
    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
        tasks = [
            baixar_video_async(session, i, item, pasta_saida, total)
            for i, item in enumerate(selecionados, 1)
        ]
        results = await asyncio.gather(*tasks)
    print(f"🚀 Download finalizado. Total: {total} vídeos")
    return results
