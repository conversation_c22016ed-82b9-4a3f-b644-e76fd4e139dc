from googleapiclient import discovery
from googleapiclient.errors import HttpError
from google.oauth2 import service_account
from datetime import datetime
import time
import sys

# no windows executar mstsc


# ... (Todas as funções de criação e verificação permanecem as mesmas) ...
SERVICE_ACCOUNT_FILE = 'google_cloud/controler.json'
# PROJECT = 'automatizacaoyoutube-463617'
PROJECT = 'prime-script-464021-t8'
ZONE = 'southamerica-east1-a' #'us-central1-a'
PREFIX = 'interface-youtube-'
VM_TAG = 'rdp-access'

timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
VM_NAME = f"{PREFIX}{timestamp}"

try:
    credentials = service_account.Credentials.from_service_account_file(SERVICE_ACCOUNT_FILE)
    compute = discovery.build('compute', 'v1', credentials=credentials)
except Exception as e:
    print(f"❌ Erro ao carregar credenciais ou construir o serviço: {e}"); sys.exit()

def wait_for_operation(operation, is_global=False):
    op_type = "Global" if is_global else "Zonal"
    print(f"⏳ Aguardando operação {op_type} finalizar...")
    while True:
        if is_global: result = compute.globalOperations().get(project=PROJECT, operation=operation['name']).execute()
        else: result = compute.zoneOperations().get(project=PROJECT, zone=ZONE, operation=operation['name']).execute()
        if result['status'] == 'DONE':
            print(f"✅ Operação {op_type} concluída!")
            if 'error' in result: raise Exception(f"Erro na operação: {result['error']}")
            return result
        time.sleep(3)

def create_or_get_firewall_rule():
    firewall_rule_name = 'allow-rdp-3389'
    try:
        compute.firewalls().get(project=PROJECT, firewall=firewall_rule_name).execute()
        print(f"👍 Regra de firewall '{firewall_rule_name}' já existe.")
    except HttpError as e:
        if e.resp.status == 404:
            print(f" firewall '{firewall_rule_name}' não encontrada. Criando...")
            firewall_body = { 'name': firewall_rule_name, 'description': 'Permite conexões RDP', 'network': f'global/networks/default', 'allowed': [{'IPProtocol': 'tcp', 'ports': ['3389']}], 'targetTags': [VM_TAG], 'sourceRanges': ['0.0.0.0/0']}
            op = compute.firewalls().insert(project=PROJECT, body=firewall_body).execute()
            wait_for_operation(op, is_global=True)
        else: raise e

def verify_startup_script(vm_name):
    print("\n" + "="*50)
    print("🔎 Iniciando verificação do script de inicialização (versão PPA)...")
    print("Isso pode levar de 5 a 7 minutos. Monitorando logs...")
    print("="*50)
    success_marker = "SUCCESS: Startup script finished."
    timeout = 800
    start_time = time.time()
    printed_messages = {}
    while time.time() - start_time < timeout:
        try:
            req = compute.instances().getSerialPortOutput(project=PROJECT, zone=ZONE, instance=vm_name, port=1)
            output = req.execute()
            log_content = output.get('contents', '')
            def print_once(key, message):
                if key not in printed_messages: print(message); printed_messages[key] = True
            if "STEP_XFCE_START" in log_content: print_once("p1", "... Etapa 1/4: Instalando XFCE e XRDP...")
            if "STEP_FIREFOX_PPA_START" in log_content: print_once("p2", "... Etapa 2/4: Configurando PPA e instalando Firefox (versão .deb)...")
            if "FIREFOX_VERIFIED" in log_content: print_once("p3", "    ↳ ✅ Firefox (.deb) instalado e verificado!")
            if "STEP_SHORTCUT_START" in log_content: print_once("p4", "... Etapa 3/4: Criando atalho na Área de Trabalho...")
            if "SHORTCUT_CREATED" in log_content: print_once("p5", "    ↳ ✅ Atalho do Firefox criado!")
            if "STEP_FINAL_CONFIG_START" in log_content: print_once("p6", "... Etapa 4/4: Configurações finais...")
            if "CRITICAL_ERROR" in log_content: print("\n❌ ERRO CRÍTICO no script. Abortando."); return False
            if success_marker in log_content: print("\n" + "="*50); print("✅ SUCESSO! Ambiente robusto foi instalado e configurado."); print("="*50); return True
        except HttpError as e:
            if e.resp.status != 404: print(f"Aguardando logs... (API: {e.resp.status})")
        time.sleep(15)
    print("\n" + "="*50); print("❌ ERRO: Timeout. A instalação falhou ou está muito lenta."); print("="*50)
    return False
    
def create_instance():
    startup_script = '''#!/bin/bash
set -e 
echo "STARTUP SCRIPT STARTED (PPA VERSION)"
echo "STEP_XFCE_START"
apt-get update
apt-get -y install software-properties-common unzip
while fuser /var/lib/dpkg/lock-frontend >/dev/null 2>&1; do echo "Aguardando apt..."; sleep 10; done
apt-get -y install xfce4 xfce4-goodies xrdp
echo "STEP_FIREFOX_PPA_START"
snap remove --purge firefox || echo "Snap do Firefox não encontrado, o que é bom."
add-apt-repository -y ppa:mozillateam/ppa
echo '
Package: *
Pin: release o=LP-PPA-mozillateam
Pin-Priority: 1001
' > /etc/apt/preferences.d/mozilla-firefox
apt-get update
apt-get -y install firefox
command -v firefox &>/dev/null || { echo "CRITICAL_ERROR: Verificação do Firefox falhou."; exit 1; }
echo "FIREFOX_VERIFIED"
echo "STEP_SHORTCUT_START"
mkdir -p /home/<USER>/Desktop
cp /usr/share/applications/firefox.desktop /home/<USER>/Desktop/
chown ubuntu:ubuntu /home/<USER>/Desktop/firefox.desktop
chmod +x /home/<USER>/Desktop/firefox.desktop
echo "SHORTCUT_CREATED"
echo "STEP_FINAL_CONFIG_START"
echo "startxfce4" > /etc/xrdp/startwm.sh
systemctl enable xrdp
systemctl restart xrdp
echo "ubuntu:senha123" | chpasswd
echo "SUCCESS: Startup script finished."
'''
    config = { 'name': VM_NAME, 'machineType': f"zones/{ZONE}/machineTypes/e2-medium", 'tags': {'items': [VM_TAG]}, 'disks': [{'boot': True, 'autoDelete': True, 'initializeParams': { 'sourceImage': 'projects/ubuntu-os-cloud/global/images/family/ubuntu-2204-lts', 'diskSizeGb': '30' }}], 'networkInterfaces': [{'network': 'global/networks/default', 'accessConfigs': [ {'type': 'ONE_TO_ONE_NAT', 'name': 'External NAT'} ]}], 'metadata': {'items': [{'key': 'startup-script', 'value': startup_script}]} }
    print(f"⚙️  Criando VM: {VM_NAME}...")
    op = compute.instances().insert(project=PROJECT, zone=ZONE, body=config).execute()
    wait_for_operation(op)
    instance = compute.instances().get(project=PROJECT, zone=ZONE, instance=VM_NAME).execute()
    return instance['networkInterfaces'][0]['accessConfigs'][0]['natIP']

def delete_instance(vm_name_to_delete):
    print(f"\n🛑 Excluindo VM {vm_name_to_delete} e seus recursos associados...")
    try:
        op = compute.instances().delete(project=PROJECT, zone=ZONE, instance=vm_name_to_delete).execute()
        wait_for_operation(op)
        print(f"✅ VM {vm_name_to_delete} excluída com sucesso!")
    except HttpError as e:
        if e.resp.status == 404: print(f"⚠️  A VM {vm_name_to_delete} não foi encontrada (provavelmente já foi excluída).")
        else: raise e

if __name__ == "__main__":
    delete_on_exit = True
    vm_created_successfully = False
    
    try:
        create_or_get_firewall_rule()
        public_ip = create_instance()
        vm_created_successfully = True 
        
        if verify_startup_script(VM_NAME):
            print("\n" + "#"*60)
            print("🎉 AMBIENTE PRONTO! Use o método de Mapeamento de Drive do RDP para transferir arquivos. 🎉")
            print("#"*60)
            print(f"➡  IP público: {public_ip}")
            print(f"➡  Usuário: ubuntu  | Senha: senha123")
            
            # PAINEL DE CONTROLE DA VM
            print("\n" + "#"*60); print("PAINEL DE CONTROLE DA VM"); print("#"*60)
            print("\nA VM está rodando. Este script agora é seu painel de controle.")
            print("🛑 IMPORTANTE: Fechar esta janela (Ctrl+C) irá DELETAR a VM automaticamente.")
            
            while True:
                try:
                    print("\n" + "-"*60)
                    user_command = input("➡️  Digite 'excluir' para deletar a VM ou 'manter' para fechar o script e MANTER a VM rodando: ").lower()
                    if user_command == 'excluir':
                        print("Comando 'excluir' recebido. A VM será deletada.")
                        break 
                    elif user_command == 'manter':
                        print("Comando 'manter' recebido. O script irá fechar e a VM continuará rodando.")
                        delete_on_exit = False 
                        break 
                    else:
                        print("Comando inválido.")
                except KeyboardInterrupt:
                    raise 

        else:
            print("\nFalha na criação ou verificação da VM. A exclusão será acionada.")

    except KeyboardInterrupt:
        print("\n\nInterrupção detectada (Ctrl+C). Acionando mecanismo de segurança para excluir a VM...")
    except Exception as e:
        print(f"\n❌ Ocorreu um erro crítico no script: {e}")
        print("Acionando mecanismo de segurança para excluir a VM...")
    
    finally:
        print("\n--- Finalizando o script ---")
        if delete_on_exit and vm_created_successfully:
            delete_instance(VM_NAME)
        elif not vm_created_successfully:
            print("Nenhuma VM para limpar, pois a criação falhou no início.")
        else:
            print(f"A VM '{VM_NAME}' foi mantida e continuará rodando.")
            print("Lembre-se de excluí-la manualmente no Console do Google Cloud para evitar custos.")
        print("Script encerrado.")