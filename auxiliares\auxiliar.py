import os
from datetime import datetime
from natsort import natsorted
from pydub import AudioSegment
from pydub.utils import make_chunks
from pydub.effects import compress_dynamic_range
import base64
import json
import subprocess


# Define o caminho relativo para o ffmpeg
caminho_ffmpeg = os.path.join(os.getcwd(), "bin")
os.environ["PATH"] += os.pathsep + caminho_ffmpeg


def verificar_ou_criar_pasta(nome_pasta):
    """
    Verifica se uma pasta existe no caminho relativo ao .py e cria se não existir.

    Parâmetros:
    nome_pasta (str): Nome da pasta (ou caminho relativo) a ser verificado/criado.
    """
    
    
    if not os.path.exists(nome_pasta):
        os.makedirs(nome_pasta)
    #     print(f"Pasta criada: {nome_pasta}")
    # else:
    #     print(f"Pasta já existe: {nome_pasta}")
    
    return nome_pasta


def salvar_em_txt(pasta, nome_arquivo, conteudo):
    """
    Cria (ou sobrescreve) um arquivo .txt na pasta indicada com o conteúdo informado.

    Parâmetros:
    pasta (str): Caminho da pasta onde o arquivo será salvo.
    nome_arquivo (str): Nome do arquivo (com .txt no final).
    conteudo (str): Texto que será salvo no arquivo.
    """
    # Garante que a pasta existe
    if not os.path.exists(pasta):
        os.makedirs(pasta)

    # Monta o caminho completo do arquivo
    caminho_arquivo = os.path.join(pasta, nome_arquivo)

    # Salva o conteúdo no arquivo
    with open(caminho_arquivo, 'w', encoding='utf-8') as f:
        f.write(conteudo)

    print(f"Arquivo salvo em: {caminho_arquivo}")


def ler_txt(pasta, nome_arquivo):
    """
    Lê o conteúdo de um arquivo .txt em uma pasta especificada.

    Parâmetros:
    pasta (str): Caminho da pasta onde o arquivo está.
    nome_arquivo (str): Nome do arquivo (incluindo .txt).

    Retorna:
    str: Conteúdo do arquivo.
    """
    caminho_arquivo = os.path.join(pasta, nome_arquivo)

    if not os.path.exists(caminho_arquivo):
        raise FileNotFoundError(f"O arquivo não foi encontrado: {caminho_arquivo}")

    with open(caminho_arquivo, 'r', encoding='utf-8') as f:
        conteudo = f.read()

    return conteudo

# Exemplo de uso
if __name__ == "__main__":
    pasta = "saida"
    nome_arquivo = "exemplo.txt"
    try:
        texto = ler_txt(pasta, nome_arquivo)
        print("Conteúdo do arquivo:")
        print(texto)
    except Exception as e:
        print(f"Erro ao ler arquivo: {e}")


def limpar_texto(texto, chave):
    """
    Remove linhas em branco e linhas que contenham qualquer caractere/palavra da chave.

    Parâmetros:
    texto (str): O texto de entrada.
    chave (str): Uma string com os caracteres/palavras a filtrar (ex: "#*%").

    Retorna:
    str: O texto limpo.
    """
    linhas = texto.splitlines()
    linhas_limpa = []

    for linha in linhas:
        linha_strip = linha.strip()
        if not linha_strip:
            # Pula linhas em branco
            continue
        if any(c in linha_strip for c in chave):
            # Pula se tiver algum caractere/palavra da chave
            continue
        linhas_limpa.append(linha_strip)

    return "\n".join(linhas_limpa)



def text_to_srt(text):
    # Constantes baseadas no código original
    CARACTERES_POR_BLOCO = 500
    PALAVRAS_MAX_BLOCO = 100
    DURACAO_BLOCO = 30
    INTERVALO_ENTRE_BLOCOS = 10

    def formatar_tempo(segundos):
        """Formata segundos em HH:MM:SS,MMM"""
        horas = int(segundos // 3600)
        minutos = int((segundos % 3600) // 60)
        segs_restantes = int(segundos % 60)
        milissegundos = int((segundos % 1) * 1000)
        return f"{horas:02d}:{minutos:02d}:{segs_restantes:02d},{milissegundos:03d}"

    def formatar_bloco_srt(contador, tempo_inicio, texto):
        """Formata um bloco SRT com número, tempo e texto"""
        tempo_fim = tempo_inicio + DURACAO_BLOCO
        return f"{contador}\n{formatar_tempo(tempo_inicio)} --> {formatar_tempo(tempo_fim)}\n{texto.strip()}\n\n"

    # Inicialização
    srt = ""
    contador = 1
    tempo_acumulado = 0
    palavras = text.split()
    bloco_atual = ""
    palavras_no_bloco = 0

    # Processamento das palavras
    for palavra in palavras:
        if len(bloco_atual) + len(palavra) <= CARACTERES_POR_BLOCO and palavras_no_bloco < PALAVRAS_MAX_BLOCO:
            bloco_atual += palavra + " "
            palavras_no_bloco += 1
        else:
            ultimo_ponto_final = bloco_atual.rfind(".")
            if ultimo_ponto_final != -1 and ultimo_ponto_final != len(bloco_atual) - 1:
                resto = bloco_atual[ultimo_ponto_final + 1:]
                bloco_atual = bloco_atual[:ultimo_ponto_final + 1]
                srt += formatar_bloco_srt(contador, tempo_acumulado, bloco_atual)
                contador += 1
                tempo_acumulado += DURACAO_BLOCO + INTERVALO_ENTRE_BLOCOS
                bloco_atual = resto + palavra + " "
                palavras_no_bloco = len(resto.split()) + 1
            else:
                srt += formatar_bloco_srt(contador, tempo_acumulado, bloco_atual)
                contador += 1
                tempo_acumulado += DURACAO_BLOCO + INTERVALO_ENTRE_BLOCOS
                bloco_atual = palavra + " "
                palavras_no_bloco = 1

    # Adiciona o último bloco, se houver
    if bloco_atual:
        srt += formatar_bloco_srt(contador, tempo_acumulado, bloco_atual)

    return srt.strip()


def timestamp_com_underscore():
    """
    Retorna a data e hora atual no formato 'yyyy_mm_dd_hh_mm_ss' como string.
    Exemplo: '2025_06_23_18_30_45'
    """
    now = datetime.now()
    return now.strftime("%Y_%m_%d_%H_%M_%S")


def divida_o_texto(text, max_chars):
    if max_chars <= 0 or not text:
        return []
    sentences = [s.strip() + '.' for s in text.split('.') if s.strip()]
    blocks = []
    current_block = []
    current_length = 0
    for sentence in sentences:
        sentence_length = len(sentence)
        if current_block and current_length + sentence_length + 1 <= max_chars:
            current_block.append(sentence)
            current_length += sentence_length + 1
        elif not current_block and sentence_length <= max_chars:
            current_block.append(sentence)
            current_length = sentence_length
        else:
            if current_block:
                blocks.append(' '.join(current_block))
                current_block = []
                current_length = 0
            if sentence_length > max_chars:
                start = 0
                while start < len(sentence):
                    block = sentence[start:start + max_chars]
                    if not block.endswith('.'):
                        last_dot = block.rfind('.')
                        if last_dot != -1:
                            block = block[:last_dot + 1]
                    blocks.append(block)
                    start += len(block)
            else:
                current_block.append(sentence)
                current_length = sentence_length
    if current_block:
        blocks.append(' '.join(current_block))
    return [block.strip() for block in blocks if block]

def listar_pastas(pasta_base):
    """
    Lista apenas as pastas de primeiro nível dentro de pasta_base.

    Parâmetros:
    pasta_base (str): Caminho da pasta onde procurar.

    Retorna:
    list: Lista com nomes das pastas (não inclui subpastas).
    """
    try:
        return [
            nome for nome in os.listdir(pasta_base)
            if os.path.isdir(os.path.join(pasta_base, nome))
        ]
    except FileNotFoundError:
        print(f"Pasta não encontrada: {pasta_base}")
        return []
    except PermissionError:
        print(f"Permissão negada ao acessar: {pasta_base}")
        return []
    
def listar_arquivos(pasta_base):
    """
    Lista apenas os arquivos de primeiro nível dentro de pasta_base.

    Parâmetros:
    pasta_base (str): Caminho da pasta onde procurar.

    Retorna:
    list: Lista com nomes dos arquivos (não inclui arquivos em subpastas).
    """
    try:
        return [
            nome for nome in os.listdir(pasta_base)
            if os.path.isfile(os.path.join(pasta_base, nome))
        ]
    except FileNotFoundError:
        print(f"Pasta não encontrada: {pasta_base}")
        return []
    except PermissionError:
        print(f"Permissão negada ao acessar: {pasta_base}")
        return []
    

def verifica_se_pasta_existe(caminho_pasta):
    """
    Verifica se a pasta existe.

    Parâmetros:
    caminho_pasta (str): Caminho da pasta a ser verificada.

    Retorna:
    bool: True se a pasta existe, False caso contrário.
    """
    return os.path.isdir(caminho_pasta)



def concatenar_audios(pasta_origem, pasta_saida, nome_arquivo_saida=None):
    """
    Concatena todos os arquivos de áudio (.wav e .mp3) de uma pasta em um único arquivo .wav,
    ordenando corretamente pelo número no nome do arquivo.

    Args:
        pasta_origem (str): Caminho da pasta que contém os arquivos de áudio.
        pasta_saida (str): Caminho da pasta onde o arquivo final será salvo.
        nome_arquivo_saida (str, opcional): Nome do arquivo de saída. Se None, gera automaticamente.

    Returns:
        str: Caminho completo do arquivo de saída gerado.
    """
    if not os.path.exists(pasta_origem):
        raise FileNotFoundError(f"A pasta de origem '{pasta_origem}' não foi encontrada.")

    if not os.path.exists(pasta_saida):
        os.makedirs(pasta_saida)
        print(f"Pasta de saída criada: {pasta_saida}")

    if nome_arquivo_saida is None:
        nome_arquivo_saida = f"{os.path.basename(pasta_origem)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
    
    # Buscar arquivos .wav e .mp3 e ordenar naturalmente
    arquivos_audio = natsorted([
        f for f in os.listdir(pasta_origem) if f.endswith(".wav") or f.endswith(".mp3")
    ])
    
    if not arquivos_audio:
        raise FileNotFoundError("Nenhum arquivo de áudio (.wav ou .mp3) encontrado na pasta.")
    
    # Função auxiliar para carregar áudio conforme extensão
    def carregar_audio(caminho_arquivo):
        if caminho_arquivo.endswith(".wav"):
            return AudioSegment.from_wav(caminho_arquivo)
        elif caminho_arquivo.endswith(".mp3"):
            return AudioSegment.from_mp3(caminho_arquivo)
        else:
            raise ValueError(f"Formato não suportado: {caminho_arquivo}")
    
    # Carregar o primeiro arquivo
    caminho_primeiro_arquivo = os.path.join(pasta_origem, arquivos_audio[0])
    audio_final = carregar_audio(caminho_primeiro_arquivo)
    
    # Concatenar os demais arquivos
    for arquivo in arquivos_audio[1:]:
        caminho_arquivo = os.path.join(pasta_origem, arquivo)
        audio_segmento = carregar_audio(caminho_arquivo)
        audio_final += audio_segmento
    
    # Exportar para um arquivo único .wav
    caminho_saida = os.path.join(pasta_saida, nome_arquivo_saida)
    audio_final.export(caminho_saida, format="wav")
    
    return caminho_saida

def salvar_base64_como_imagem(base64_str, pasta_destino, nome_arquivo):
    """
    Salva uma imagem codificada em base64 como um arquivo local.

    Parâmetros:
    - base64_str (str): String base64 da imagem.
    - pasta_destino (str): Caminho da pasta onde a imagem será salva.
    - nome_arquivo (str): Nome do arquivo de saída (ex: "imagem.jpg" ou "foto.png").

    Retorna:
    - caminho completo do arquivo salvo.
    """
    # Garante que a pasta existe
    os.makedirs(pasta_destino, exist_ok=True)

    # Remove cabeçalho data:image/...base64, se existir
    if "," in base64_str:
        base64_str = base64_str.split(",")[1]

    # Decodifica o base64
    imagem_bytes = base64.b64decode(base64_str)

    # Caminho completo
    caminho_arquivo = os.path.join(pasta_destino, nome_arquivo)

    # Salva o arquivo
    with open(caminho_arquivo, "wb") as f:
        f.write(imagem_bytes)

    return caminho_arquivo


import wave
import os

def criar_audio_mudo(minutos, caminho_saida="audios", nome_arquivo="audio_mudo.wav", sample_rate=44100):
    """
    Cria um arquivo de áudio mudo (.wav) com duração especificada em minutos.

    Parâmetros:
        minutos (float): Duração do áudio em minutos.
        caminho_saida (str): Pasta onde o áudio será salvo.
        nome_arquivo (str): Nome do arquivo de saída.
        sample_rate (int): Taxa de amostragem (padrão: 44100 Hz).
    """
    duracao_segundos = int(minutos * 60)
    num_amostras = duracao_segundos * sample_rate

    if not os.path.exists(caminho_saida):
        os.makedirs(caminho_saida)

    caminho_completo = os.path.join(caminho_saida, nome_arquivo)

    with wave.open(caminho_completo, 'w') as wav_file:
        wav_file.setnchannels(1)            # Mono
        wav_file.setsampwidth(2)            # 16 bits por amostra (2 bytes)
        wav_file.setframerate(sample_rate)  # Taxa de amostragem
        wav_file.writeframes(b'\x00\x00' * num_amostras)  # Dados de silêncio

    print(f"Áudio mudo criado com sucesso: {caminho_completo}")

def concatenar_audios_melhorado(pasta_origem, pasta_saida, nome_arquivo_saida=None, volume_alvo_dbfs=-20.0, tamanho_janela_ms=500):
    """
    Concatena arquivos de áudio (.wav e .mp3) de uma pasta em um único .wav,
    corrigindo partes com volume baixo para atingir um padrão.

    Args:
        pasta_origem (str): Caminho da pasta dos arquivos de áudio.
        pasta_saida (str): Caminho onde o arquivo final será salvo.
        nome_arquivo_saida (str, opcional): Nome do arquivo de saída.
        volume_alvo_dbfs (float, opcional): Volume alvo em dBFS. Padrão: -20.0
        tamanho_janela_ms (int, opcional): Tamanho da janela de ajuste em ms. Padrão: 500

    Returns:
        str: Caminho completo do arquivo de saída.
    """
    if not os.path.exists(pasta_origem):
        raise FileNotFoundError(f"A pasta de origem '{pasta_origem}' não foi encontrada.")

    if not os.path.exists(pasta_saida):
        os.makedirs(pasta_saida)
        print(f"Pasta de saída criada: {pasta_saida}")

    if nome_arquivo_saida is None:
        nome_arquivo_saida = f"{os.path.basename(pasta_origem)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"

    # Buscar arquivos e ordenar
    arquivos_audio = natsorted([
        f for f in os.listdir(pasta_origem) if f.endswith(".wav") or f.endswith(".mp3")
    ])

    if not arquivos_audio:
        raise FileNotFoundError("Nenhum arquivo de áudio (.wav ou .mp3) encontrado na pasta.")

    def carregar_audio(caminho_arquivo):
        if caminho_arquivo.endswith(".wav"):
            return AudioSegment.from_wav(caminho_arquivo)
        elif caminho_arquivo.endswith(".mp3"):
            return AudioSegment.from_mp3(caminho_arquivo)
        else:
            raise ValueError(f"Formato não suportado: {caminho_arquivo}")

    # Concatenar
    audio_final = AudioSegment.empty()
    for arquivo in arquivos_audio:
        caminho_arquivo = os.path.join(pasta_origem, arquivo)
        audio_segmento = carregar_audio(caminho_arquivo)
        audio_final += audio_segmento

    # Corrigir volume por janela
    chunks = make_chunks(audio_final, tamanho_janela_ms)
    audio_corrigido = AudioSegment.empty()

    for i, chunk in enumerate(chunks):
        diferenca = volume_alvo_dbfs - chunk.dBFS
        if diferenca > 1.0:  # margem de 1 dB
            print(f"Janela {i}: volume {chunk.dBFS:.2f} dBFS — subindo {diferenca:.2f} dB")
            chunk = chunk.apply_gain(diferenca)
        else:
            print(f"Janela {i}: volume {chunk.dBFS:.2f} dBFS — ok")

        audio_corrigido += chunk

    # Exportar
    caminho_saida = os.path.join(pasta_saida, nome_arquivo_saida)
    audio_corrigido.export(caminho_saida, format="wav")

    return caminho_saida


def extrair_dicionario_json(caminho_do_arquivo: str) -> dict:
    """
    Lê um arquivo que contém um JSON dentro de um bloco de código Markdown,
    extrai o JSON, o converte para um dicionário único e o retorna.

    Args:
        caminho_do_arquivo (str): O caminho completo para o arquivo de texto.

    Returns:
        dict: Um dicionário com todo o conteúdo JSON combinado.
              Retorna um dicionário vazio se ocorrer um erro, o arquivo
              não for encontrado ou não puder ser processado.
    """
    # Garante que o caminho do arquivo existe antes de prosseguir
    if not os.path.exists(caminho_do_arquivo):
        print(f"Erro: O arquivo não foi encontrado em '{caminho_do_arquivo}'")
        return {}

    try:
        with open(caminho_do_arquivo, 'r', encoding='utf-8-sig') as f:
            # Lê o conteúdo bruto do arquivo
            conteudo_bruto = f.read().strip()

        # Remove o ```json do início e o ``` do final
        if conteudo_bruto.startswith('```json') and conteudo_bruto.endswith('```'):
            # Pega o conteúdo a partir do final de '```json\n' até o início de '```'
            conteudo_puro = conteudo_bruto[len('```json'):-len('```')].strip()
        else:
            # Se não tiver o embrulho, assume que já é um JSON puro
            conteudo_puro = conteudo_bruto
        
        # Converte a string limpa para uma lista de dicionários Python
        lista_de_titulos = json.loads(conteudo_puro)
        
        # Transforma a lista de dicionários em um único dicionário
        dicionario_final = {}
        for item in lista_de_titulos:
            dicionario_final.update(item)
            
        return dicionario_final

    except json.JSONDecodeError:
        print(f"Erro: Falha ao decodificar o JSON no arquivo '{caminho_do_arquivo}'. Verifique o conteúdo.")
        return {}
    except Exception as e:
        print(f"Ocorreu um erro inesperado: {e}")
        return {}
    
# def concatenar_audios(
#     pasta_origem: str, 
#     pasta_saida: str, 
#     nome_arquivo_saida: str
# ) -> str:
#     """
#     Função 'plug-and-play' otimizada para concatenar, nivelar e normalizar áudios.
#     Pronta para ser importada em outros scripts.

#     Args:
#         pasta_origem (str): A pasta onde estão os áudios de entrada.
#         pasta_saida (str): A pasta onde o áudio final será salvo.
#         nome_arquivo_saida (str): O nome do arquivo final (ex: 'resultado.mp3').

#     Returns:
#         str: O caminho completo do arquivo final gerado, ou None se falhar.
#     """
#     # --- Configurações Internas (Automáticas e Otimizadas) ---
#     COMPRESSOR_THRESHOLD_DBFS = -20.0
#     COMPRESSOR_RATIO = 4.0
#     DURACAO_SILENCIO_MS = 400
#     CROSSFADE_MS = 15
#     # --- Fim das Configurações ---

#     print(f"Iniciando processo para gerar '{nome_arquivo_saida}'...")

#     if not os.path.exists(pasta_origem):
#         print(f"ERRO: A pasta de origem '{pasta_origem}' não foi encontrada.")
#         return None

#     if not os.path.exists(pasta_saida):
#         print(f"-> Pasta de saída '{pasta_saida}' não encontrada. Criando...")
#         os.makedirs(pasta_saida)

#     try:
#         arquivos_paths = natsorted([
#             os.path.join(pasta_origem, f) 
#             for f in os.listdir(pasta_origem) if f.lower().endswith((".wav", ".mp3"))
#         ])
#         if not arquivos_paths:
#             raise FileNotFoundError
#     except FileNotFoundError:
#         print(f"ERRO: Nenhum arquivo de áudio (.wav ou .mp3) encontrado em '{pasta_origem}'.")
#         return None

#     print("-> Etapa 1 de 3: Processando e nivelando áudios individuais...")
#     segmentos_processados = []
#     for path in arquivos_paths:
#         try:
#             audio = AudioSegment.from_file(path)
#             audio_comprimido = compress_dynamic_range(
#                 audio, 
#                 threshold=COMPRESSOR_THRESHOLD_DBFS, 
#                 ratio=COMPRESSOR_RATIO
#             )
#             segmentos_processados.append(audio_comprimido)
#         except Exception as e:
#             print(f"  [AVISO] Ignorando arquivo {os.path.basename(path)}: {e}")

#     if not segmentos_processados:
#         print("ERRO: Nenhum arquivo pôde ser processado.")
#         return None

#     print("-> Etapa 2 de 3: Normalizando e unindo os áudios...")
#     dbfs_alvo = max(s.dBFS for s in segmentos_processados)
    
#     silencio = AudioSegment.silent(duration=DURACAO_SILENCIO_MS)
#     audio_final = AudioSegment.empty()

#     for i, seg in enumerate(segmentos_processados):
#         ganho = dbfs_alvo - seg.dBFS
#         seg_normalizado = seg.apply_gain(ganho)
        
#         if i == 0:
#             audio_final = seg_normalizado
#         else:
#             audio_final = audio_final.append(silencio, crossfade=0)
#             audio_final = audio_final.append(seg_normalizado, crossfade=CROSSFADE_MS)

#     print(f"-> Etapa 3 de 3: Exportando arquivo final...")
#     try:
#         formato = nome_arquivo_saida.split('.')[-1].lower()
#         caminho_final = os.path.join(pasta_saida, nome_arquivo_saida)
#         audio_final.export(caminho_final, format=formato, bitrate="192k" if formato == "mp3" else None)
#     except Exception as e:
#         print(f"ERRO ao exportar o arquivo final: {e}")
#         return None
        
#     print(f"\nPROCESSO CONCLUÍDO!\nArquivo final salvo em: {caminho_final}")
#     return caminho_final


# def concatenar_audios(pasta_origem, pasta_saida, nome_arquivo_saida=None):
#     """
#     Concatena os áudios da pasta após nivelar o volume ao maior dBFS encontrado.

#     Args:
#         pasta_origem (str): Pasta com os áudios de entrada (.wav ou .mp3).
#         pasta_saida (str): Pasta onde salvar o áudio final.
#         nome_arquivo_saida (str): Nome do arquivo final. Se None, cria automático.

#     Returns:
#         str: Caminho completo do arquivo final gerado.
#     """
#     if not os.path.exists(pasta_origem):
#         raise FileNotFoundError(f"A pasta de origem '{pasta_origem}' não foi encontrada.")

#     if not os.path.exists(pasta_saida):
#         os.makedirs(pasta_saida)
#         print(f"Pasta de saída criada: {pasta_saida}")

#     if nome_arquivo_saida is None:
#         nome_arquivo_saida = f"{os.path.basename(pasta_origem)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"

#     # Listar e ordenar
#     arquivos_audio = natsorted([
#         f for f in os.listdir(pasta_origem) if f.endswith(".wav") or f.endswith(".mp3")
#     ])

#     if not arquivos_audio:
#         raise FileNotFoundError("Nenhum arquivo de áudio (.wav ou .mp3) encontrado na pasta.")

#     audios = {}
#     volumes = {}

#     # Carrega todos os áudios e mede dBFS
#     for arquivo in arquivos_audio:
#         caminho = os.path.join(pasta_origem, arquivo)
#         if arquivo.endswith(".wav"):
#             audio = AudioSegment.from_wav(caminho)
#         else:
#             audio = AudioSegment.from_mp3(caminho)

#         audios[arquivo] = audio
#         volumes[arquivo] = audio.dBFS

#     # Descobrir o maior dBFS
#     maior_dbfs = max(volumes.values())
#     print(f"Maior volume: {maior_dbfs:.2f} dBFS")

#     # Nivelar volumes e concatenar
#     audio_final = AudioSegment.empty()
#     for arquivo in arquivos_audio:
#         audio = audios[arquivo]
#         ganho = maior_dbfs - audio.dBFS
#         audio_nivelado = audio.apply_gain(ganho)
#         print(f"{arquivo}: {volumes[arquivo]:.2f} dBFS -> +{ganho:.2f} dB")
#         audio_final += audio_nivelado

#     # Exportar
#     caminho_saida = os.path.join(pasta_saida, nome_arquivo_saida)
#     audio_final.export(caminho_saida, format="wav")
#     print(f"Áudio final salvo em: {caminho_saida}")

#     return caminho_saida

# def concatenar_audios(pasta_origem, pasta_saida, nome_arquivo_saida=None):
#     """
#     Concatena os áudios da pasta após nivelar o volume ao maior dBFS encontrado.

#     Args:
#         pasta_origem (str): Pasta com os áudios de entrada (.wav ou .mp3).
#         pasta_saida (str): Pasta onde salvar o áudio final.
#         nome_arquivo_saida (str): Nome do arquivo final. Se None, cria automático.

#     Returns:
#         str: Caminho completo do arquivo final gerado.
#     """
#     if not os.path.exists(pasta_origem):
#         raise FileNotFoundError(f"A pasta de origem '{pasta_origem}' não foi encontrada.")

#     if not os.path.exists(pasta_saida):
#         os.makedirs(pasta_saida)
#         print(f"Pasta de saída criada: {pasta_saida}")

#     if nome_arquivo_saida is None:
#         nome_arquivo_saida = f"{os.path.basename(pasta_origem)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"

#     # Listar e ordenar
#     arquivos_audio = natsorted([
#         f for f in os.listdir(pasta_origem) if f.endswith(".wav") or f.endswith(".mp3")
#     ])

#     if not arquivos_audio:
#         raise FileNotFoundError("Nenhum arquivo de áudio (.wav ou .mp3) encontrado na pasta.")

#     audios = []
#     volumes = []

#     # Carrega áudios e mede dBFS
#     for arquivo in arquivos_audio:
#         caminho = os.path.join(pasta_origem, arquivo)
#         if arquivo.endswith(".wav"):
#             audio = AudioSegment.from_wav(caminho)
#         else:
#             audio = AudioSegment.from_mp3(caminho)

#         audios.append(audio)
#         volumes.append(audio.dBFS)

#     # Descobrir o maior dBFS
#     maior_dbfs = max(volumes)
#     print(f"Maior volume: {maior_dbfs:.2f} dBFS")

#     # Nivelar volumes e concatenar
#     audio_final = AudioSegment.empty()
#     for audio, vol, arquivo in zip(audios, volumes, arquivos_audio):
#         ganho = maior_dbfs - vol
#         audio_nivelado = audio.apply_gain(ganho)
#         print(f"{arquivo}: {vol:.2f} dBFS -> +{ganho:.2f} dB")
#         audio_final += audio_nivelado

#     # Exportar
#     caminho_saida = os.path.join(pasta_saida, nome_arquivo_saida)
#     audio_final.export(caminho_saida, format="wav")
#     print(f"Áudio final salvo em: {caminho_saida}")

#     return caminho_saida

# def concatenar_audios(pasta_origem, pasta_saida, nome_arquivo_saida=None, dbfs_alvo=-6.0):
#     """
#     Concatena os áudios da pasta após nivelar para um volume seguro (ex: -6 dBFS).

#     Args:
#         pasta_origem (str): Pasta com os áudios de entrada (.wav ou .mp3).
#         pasta_saida (str): Pasta onde salvar o áudio final.
#         nome_arquivo_saida (str): Nome do arquivo final. Se None, cria automático.
#         dbfs_alvo (float): dBFS alvo seguro para normalização.

#     Returns:
#         str: Caminho completo do arquivo final gerado.
#     """
#     if not os.path.exists(pasta_origem):
#         raise FileNotFoundError(f"A pasta de origem '{pasta_origem}' não foi encontrada.")

#     if not os.path.exists(pasta_saida):
#         os.makedirs(pasta_saida)
#         print(f"Pasta de saída criada: {pasta_saida}")

#     if nome_arquivo_saida is None:
#         nome_arquivo_saida = f"{os.path.basename(pasta_origem)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"

#     arquivos_audio = natsorted([
#         f for f in os.listdir(pasta_origem) if f.endswith(".wav") or f.endswith(".mp3")
#     ])

#     if not arquivos_audio:
#         raise FileNotFoundError("Nenhum arquivo de áudio (.wav ou .mp3) encontrado na pasta.")

#     audio_final = AudioSegment.empty()

#     for arquivo in arquivos_audio:
#         caminho = os.path.join(pasta_origem, arquivo)
#         if arquivo.endswith(".wav"):
#             audio = AudioSegment.from_wav(caminho)
#         else:
#             audio = AudioSegment.from_mp3(caminho)

#         atual_dbfs = audio.dBFS
#         ganho = dbfs_alvo - atual_dbfs
#         audio_nivelado = audio.apply_gain(ganho)
        
#         print(f"{arquivo}: {atual_dbfs:.2f} dBFS -> +{ganho:.2f} dB (alvo {dbfs_alvo} dBFS)")
        
#         audio_final += audio_nivelado

#     caminho_saida = os.path.join(pasta_saida, nome_arquivo_saida)
#     audio_final.export(caminho_saida, format="wav")
#     print(f"Áudio final salvo em: {caminho_saida}")

#     return caminho_saida

# def concatenar_audios(pasta_origem, pasta_saida, nome_arquivo_saida=None, dbfs_alvo=-1.0):
#     """
#     Concatena todos os áudios e normaliza o resultado para o dBFS alvo.

#     Args:
#         pasta_origem (str): Pasta com os áudios de entrada (.wav ou .mp3).
#         pasta_saida (str): Pasta onde salvar o áudio final.
#         nome_arquivo_saida (str): Nome do arquivo final. Se None, cria automático.
#         dbfs_alvo (float): dBFS alvo seguro para normalização.

#     Returns:
#         str: Caminho completo do arquivo final gerado.
#     """
#     if not os.path.exists(pasta_origem):
#         raise FileNotFoundError(f"A pasta de origem '{pasta_origem}' não foi encontrada.")

#     if not os.path.exists(pasta_saida):
#         os.makedirs(pasta_saida)
#         print(f"Pasta de saída criada: {pasta_saida}")

#     if nome_arquivo_saida is None:
#         nome_arquivo_saida = f"{os.path.basename(pasta_origem)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"

#     # Listar arquivos
#     arquivos_audio = natsorted([
#         f for f in os.listdir(pasta_origem) if f.endswith(".wav") or f.endswith(".mp3")
#     ])

#     if not arquivos_audio:
#         raise FileNotFoundError("Nenhum arquivo de áudio (.wav ou .mp3) encontrado na pasta.")

#     # Concatenar todos
#     audio_final = AudioSegment.empty()
#     for arquivo in arquivos_audio:
#         caminho = os.path.join(pasta_origem, arquivo)
#         audio = AudioSegment.from_file(caminho)
#         print(f"{arquivo}: {audio.dBFS:.2f} dBFS")
#         audio_final += audio

#     # Ajuste final do volume
#     dBFS_atual = audio_final.dBFS
#     ganho = dbfs_alvo - dBFS_atual
#     print(f"\nÁudio concatenado: {dBFS_atual:.2f} dBFS")
#     print(f"Aplicando ganho de {ganho:.2f} dB para atingir {dbfs_alvo:.2f} dBFS")
#     audio_final = audio_final.apply_gain(ganho)

#     # Exportar
#     caminho_saida = os.path.join(pasta_saida, nome_arquivo_saida)
#     audio_final.export(caminho_saida, format="wav")
#     print(f"Áudio final salvo em: {caminho_saida}")

#     return caminho_saida
# def concatenar_audios(
#     pasta_origem: str, 
#     pasta_saida: str, 
#     nome_arquivo_saida: str
# ) -> str:
#     """
#     Função simplificada para concatenar e processar áudios.
    
#     Aplica automaticamente compressão para picos e normalização para um
#     volume consistente, com pausas e transições suaves.

#     Args:
#         pasta_origem (str): A pasta onde estão os áudios a serem unidos.
#         pasta_saida (str): A pasta onde o áudio final será salvo.
#         nome_arquivo_saida (str): O nome do arquivo final (ex: 'resultado.mp3').

#     Returns:
#         str: O caminho completo do arquivo final gerado, ou None se falhar.
#     """
#     print(f"Iniciando processo simplificado para gerar '{nome_arquivo_saida}'...")
    
#     # Extrai o formato do nome do arquivo
#     try:
#         formato = nome_arquivo_saida.split('.')[-1]
#         if not formato: # Caso o nome não tenha extensão
#             raise IndexError
#     except IndexError:
#         print("ERRO: O nome do arquivo de saída deve incluir uma extensão (ex: '.mp3' ou '.wav').")
#         return None

#     # Chama a função de processamento completa com as melhores configurações padrão
#     caminho_final = _processar_audios_interno(
#         pasta_origem=pasta_origem,
#         pasta_saida=pasta_saida,
#         nome_arquivo_saida=nome_arquivo_saida,
#         formato_saida=formato,
#         compressor_threshold=-20.0, # Valor padrão ideal para voz
#         compressor_ratio=4.0,       # Valor padrão ideal para voz
#         duracao_silencio_ms=400,    # Pausa natural padrão
#         crossfade_ms=50             # Transição suave padrão
#     )
    
#     return caminho_final


# def _processar_audios_interno(
#     pasta_origem: str,
#     pasta_saida: str,
#     nome_arquivo_saida: str,
#     formato_saida: str,
#     compressor_threshold: float,
#     compressor_ratio: float,
#     duracao_silencio_ms: int,
#     crossfade_ms: int
# ) -> str:
#     """
#     (Função Interna) Realiza o processamento completo de áudio.
#     Esta função é chamada pela `concatenar_audios` com configurações pré-definidas.
#     """
#     if not os.path.exists(pasta_origem):
#         print(f"ERRO: A pasta de origem '{pasta_origem}' não foi encontrada.")
#         return None

#     if not os.path.exists(pasta_saida):
#         os.makedirs(pasta_saida)

#     arquivos_audio_paths = natsorted([
#         os.path.join(pasta_origem, f) 
#         for f in os.listdir(pasta_origem) if f.lower().endswith((".wav", ".mp3"))
#     ])

#     if not arquivos_audio_paths:
#         print(f"ERRO: Nenhum arquivo de áudio (.wav ou .mp3) encontrado em '{pasta_origem}'.")
#         return None

#     # PASSO 1: COMPRESSÃO
#     print("\n--- Passo 1: Aplicando compressão para controlar picos internos... ---")
#     segmentos_comprimidos = []
#     for i, path in enumerate(arquivos_audio_paths):
#         try:
#             segmento = AudioSegment.from_file(path)
#             segmento_comprimido = compress_dynamic_range(
#                 segmento, threshold=compressor_threshold, ratio=compressor_ratio,
#                 attack=5.0, release=50.0
#             )
#             segmentos_comprimidos.append(segmento_comprimido)
#             print(f"  [OK] Arquivo '{os.path.basename(path)}' comprimido.")
#         except Exception as e:
#             print(f"  [AVISO] Falha ao processar '{os.path.basename(path)}': {e}")

#     if not segmentos_comprimidos:
#         print("ERRO: Nenhum arquivo pôde ser lido ou processado.")
#         return None

#     # PASSO 2: ANÁLISE PÓS-COMPRESSÃO
#     print("\n--- Passo 2: Analisando volumes para normalização... ---")
#     dbfs_alvo = max(s.dBFS for s in segmentos_comprimidos)
#     print(f"--- Volume alvo definido em {dbfs_alvo:.2f} dBFS (baseado no áudio mais alto). ---")

#     # PASSO 3: NORMALIZAÇÃO E CONCATENAÇÃO
#     print("\n--- Passo 3: Normalizando e unindo os áudios... ---")
#     audio_final = None
#     silencio = AudioSegment.silent(duration=duracao_silencio_ms)
#     for seg in segmentos_comprimidos:
#         ganho = dbfs_alvo - seg.dBFS
#         segmento_normalizado = seg.apply_gain(ganho)
#         if audio_final is None:
#             audio_final = segmento_normalizado
#         else:
#             audio_final = audio_final.append(silencio, crossfade=0)
#             audio_final = audio_final.append(segmento_normalizado, crossfade=crossfade_ms)
#     print("--- Todos os áudios foram unidos. ---")
    
#     # EXPORTAÇÃO
#     caminho_saida = os.path.join(pasta_saida, nome_arquivo_saida)
#     print(f"\n--- Exportando arquivo final para '{caminho_saida}' ---")
#     audio_final.export(caminho_saida, format=formato_saida, bitrate="192k" if formato_saida == "mp3" else None)
#     print("\nProcesso concluído com sucesso!")
    
#     return caminho_saida


# # --- EXEMPLO DE COMO USAR A FUNÇÃO SIMPLES ---
# if __name__ == '__main__':
#     # Configure aqui suas pastas
#     pasta_dos_audios = "audios_tts_para_unir"
#     pasta_de_destino = "audio_final_pronto"
    
#     # Cria pastas e arquivos de exemplo se não existirem
#     if not os.path.exists(pasta_dos_audios):
#         print("Criando pastas e arquivos de áudio de exemplo...")
#         os.makedirs(pasta_dos_audios)
#         parte_baixa = AudioSegment.sine(220, duration=800).apply_gain(-25)
#         pico_alto = AudioSegment.sine(220, duration=200).apply_gain(-2)
#         audio_com_pico = parte_baixa + pico_alto + parte_baixa
#         audio_com_pico.export(os.path.join(pasta_dos_audios, "audio_1.wav"), format="wav")
#         audio_constante = AudioSegment.sine(330, duration=1500).apply_gain(-22)
#         audio_constante.export(os.path.join(pasta_dos_audios, "audio_2.wav"), format="wav")

#     # --- É SÓ CHAMAR A FUNÇÃO SIMPLES AQUI ---
#     caminho_do_arquivo = concatenar_audios(
#         pasta_dos_audios,
#         pasta_de_destino,
#         'minha_narracao_final.mp3'
#     )

#     if caminho_do_arquivo:
#         print(f"\nArquivo final gerado em: {caminho_do_arquivo}")


# def concatenar_audios(pasta_origem):
#     """
#     Concatena todos os arquivos de áudio (.wav e .mp3) de uma pasta em um único arquivo .wav,
#     ordenando corretamente pelo número no nome do arquivo.

#     Args:
#         pasta_origem (str): Caminho da pasta que contém os arquivos de áudio.

#     Returns:
#         str: Nome do arquivo de saída gerado.
#     """
#     nome_arquivo_saida = f"{os.path.basename(pasta_origem)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.wav"
    
#     if not os.path.exists(pasta_origem):
#         raise FileNotFoundError(f"A pasta '{pasta_origem}' não foi encontrada.")
    
#     # Buscar arquivos .wav e .mp3 e ordenar naturalmente
#     arquivos_audio = natsorted([
#         f for f in os.listdir(pasta_origem) if f.endswith(".wav") or f.endswith(".mp3")
#     ])
    
#     if not arquivos_audio:
#         raise FileNotFoundError("Nenhum arquivo de áudio (.wav ou .mp3) encontrado na pasta.")
    
#     # Função auxiliar para carregar áudio conforme extensão
#     def carregar_audio(caminho_arquivo):
#         if caminho_arquivo.endswith(".wav"):
#             return AudioSegment.from_wav(caminho_arquivo)
#         elif caminho_arquivo.endswith(".mp3"):
#             return AudioSegment.from_mp3(caminho_arquivo)
#         else:
#             raise ValueError(f"Formato não suportado: {caminho_arquivo}")
    
#     # Carregar o primeiro arquivo
#     caminho_primeiro_arquivo = os.path.join(pasta_origem, arquivos_audio[0])
#     audio_final = carregar_audio(caminho_primeiro_arquivo)
    
#     # Concatenar os demais arquivos
#     for arquivo in arquivos_audio[1:]:
#         caminho_arquivo = os.path.join(pasta_origem, arquivo)
#         audio_segmento = carregar_audio(caminho_arquivo)
#         audio_final += audio_segmento
    
#     # Exportar para um arquivo único .wav
#     caminho_saida = os.path.join(pasta_origem, nome_arquivo_saida)
#     audio_final.export(caminho_saida, format="wav")
    
#     return nome_arquivo_saida

import cv2
def remover_videos_corrompidos(pasta):
    """
    Remove vídeos da pasta que não possuem duração ou não podem ser lidos com OpenCV.
    """
    extensoes = ('.mp4', '.mov', '.mkv', '.avi', '.webm')
    for nome_arquivo in os.listdir(pasta):
        if not nome_arquivo.lower().endswith(extensoes):
            continue

        caminho = os.path.join(pasta, nome_arquivo)

        try:
            cap = cv2.VideoCapture(caminho)
            total_frames = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            fps = cap.get(cv2.CAP_PROP_FPS)
            cap.release()

            duracao = (total_frames / fps) if fps > 0 else 0

            if duracao == 0 or total_frames <= 1:
                print(f"Removendo: {nome_arquivo} (sem duração detectada)")
                os.remove(caminho)

        except Exception as e:
            print(f"Erro ao tentar abrir {nome_arquivo}, removendo. Erro: {e}")
            os.remove(caminho)

import random

def embaralhar_e_renomear_videos(pasta):
    # Lista todos os vídeos da pasta
    videos = [f for f in os.listdir(pasta) if f.lower().endswith(('.mp4', '.mov', '.avi', '.mkv', '.webm'))]

    # Embaralha a ordem dos vídeos
    random.shuffle(videos)

    # Renomeia para temporários primeiro
    temporarios = []
    for nome in videos:
        caminho_atual = os.path.join(pasta, nome)
        nome_temp = f"temp_{nome}"
        caminho_temp = os.path.join(pasta, nome_temp)
        os.rename(caminho_atual, caminho_temp)
        temporarios.append(nome_temp)

    # Renomeia os arquivos temporários para nomes finais
    for idx, nome_temp in enumerate(temporarios, start=1):
        extensao = os.path.splitext(nome_temp)[1]
        novo_nome = f"video_{idx}{extensao}"
        caminho_temp = os.path.join(pasta, nome_temp)
        caminho_novo = os.path.join(pasta, novo_nome)

        print(f'Renomeando {nome_temp} -> {novo_nome}')
        os.rename(caminho_temp, caminho_novo)

    print("✅ Vídeos renomeados e embaralhados com sucesso.")